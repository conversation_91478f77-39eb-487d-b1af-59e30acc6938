<?php

namespace App\Filament\Pages\Customer;

use App\Actions\Mercos\CreateCustomersFromErpFlex;
use App\Actions\Mercos\CreateMercosCustomersFromMercos;
use App\Actions\Pairing\CustomerPairing as CustomerPairingAction;
use App\Filament\Pages\Concerns\HandlesPairingImport;
use App\Filament\Resources\CustomerResource;
use App\Models\MercosCustomer;
use App\Models\CustomerPairing as CustomerPairingModel;
use Awcodes\FilamentTableRepeater\Components\TableRepeater;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Collection;
use Throwable;

class CustomerPairing extends ListRecords
{
    use InteractsWithForms;
    use HandlesPairingImport;

    protected static ?string $title = 'Mapeamento de Clientes';
    protected static string $view = 'filament.pages.customers.customer-pairing';
    protected static string $resource = CustomerResource::class;

    protected string $routeName = 'customers.pairing';
    protected bool $mercosOriginFlow = false;

    /**
     * @var \Illuminate\Support\Collection
     */
    public $erpFlexCustomersCollection;

    public array $erpFlexCustomers;

    /**
     * @var \Illuminate\Support\Collection
     */
    public $mercosCustomers;

    /**
     * @var \Illuminate\Support\Collection
     */
    public Collection $erpFlexOptions;

    /**
     * @var \Illuminate\Support\Collection
     */
    public  Collection $mercosOptions;

    /** @inheritDoc */
    public function mount(): void
    {
        $this->setCustomerData();
    }

    /** @inheritDoc */
    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return false;
    }

    /**
     * Sets the customers pairing data
     *
     * @return void
     */
    protected function setCustomerData()
    {
        $this->erpFlexCustomersCollection = CustomerPairingModel::get();
        $this->erpFlexCustomers = $this->erpFlexCustomersCollection->toArray();
        $this->mercosCustomers = MercosCustomer::get();

        $this->erpFlexOptions = $this->erpFlexCustomersCollection->mapWithKeys(function ($item) {
            return [
                $item->erp_flex_id => !empty($item->erp_flex_data['SA1_Desc'])
                    ? $item->erp_flex_data['SA1_Desc']
                    : 'Sem nome'
            ];
        });

        $this->mercosOptions = $this->mercosCustomers->mapWithKeys(function ($item) {
            return [
                $item->mercos_id => !empty($item->mercos_data['nome_fantasia'])
                    ? $item->mercos_data['nome_fantasia']
                    : 'Sem nome'
            ];
        });
    }

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            Action::make('Aprovar mapeamento')
                ->action(function (): void {
                    try {
                        $this->onApproveMapping();
                        success_notification('O mapeamento foi realizado com sucesso')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-check')
                ->color('warning')
                ->requiresConfirmation(),
            $this->defineImportAction($this->erpFlexCustomersCollection, $this->mercosCustomers, new CustomerPairingModel()),
            Action::make('Salvar rascunho')
                ->action(function (): void {
                    try {
                        $this->onSaveMapping();
                        success_notification('O rascunho foi salvo com sucesso')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->color('success')
                ->icon('heroicon-s-save'),
            Action::make('Sincronizar dados')
                ->action(function (): void {
                    try {
                        $this->onRefreshData();
                        success_notification('Os dados foram atualizados')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->color('success')
                ->icon('heroicon-s-arrow-path')
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            Section::make('Mapear clientes')
                ->compact()
                ->schema([
                    Grid::make(1)->schema([
                        TableRepeater::make('erpFlexCustomers')
                            ->disableItemCreation()
                            ->disableItemMovement()
                            ->disableItemDeletion()
                            ->disableLabel()
                            ->label('')
                            ->columns(2)
                            ->schema([
                                Select::make('erp_flex_id')
                                    ->disabled()
                                    ->disableLabel()
                                    ->label('Cliente ErpFlex (um ID por linha)')
                                    ->options($this->erpFlexOptions),
                                Select::make('mercos_id')
                                    ->disableLabel()
                                    ->label('Cliente Mercos (um ID por linha)')
                                    ->options($this->mercosOptions)
                                    ->searchable()
                            ])
                    ])
                ]),
        ];
    }

    /**
     * Saves the current mapping collection
     *
     * @return void
     */
    public function onSaveMapping(): void
    {
        foreach ($this->erpFlexCustomers as $customer) {
            $mercosData = null;

            if (!empty($customer['mercos_id'])) {
                $mercosData = $this->mercosCustomers
                    ->where('mercos_id', $customer['mercos_id'])
                    ->first()?->mercos_data;
            }

            CustomerPairingModel::updateOrCreate(
                ['id' => $customer['id']],
                [
                    'mercos_id' => $customer['mercos_id'] ?? null,
                    'mercos_data' => $mercosData
                ]
            );
        }
    }

    /**
     * Transforms the current mapping into the proper models.
     *
     * @return mixed
     */
    public function onApproveMapping(): mixed
    {
        $this->onSaveMapping();
        CustomerPairingAction::run();

        return redirect()->to('/app/customers');
    }

    /**
     * Refreshes the data on both sides.
     *
     * @return mixed
     */
    public function onRefreshData()
    {
        CreateCustomersFromErpFlex::run(true);
        CreateMercosCustomersFromMercos::run(true);

        return redirect()->to(route('customers.pairing'));
    }
}
