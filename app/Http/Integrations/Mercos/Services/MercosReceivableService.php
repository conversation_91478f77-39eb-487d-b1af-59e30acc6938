<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderReceivable;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosCreateReceivableResponse;
use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\Order\MercosCreateReceivableRequest;

class MercosReceivableService extends MercosBaseService
{
    use HandlesLogCreation;

    /**
     * Create a create a receivable from an order.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderReceivable $mercosReceivable
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosCreateReceivableResponse
     */
    public function createReceivable(MercosOrderReceivable $mercosReceivable): MercosCreateReceivableResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosCreateReceivableRequest($mercosReceivable))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosCreateReceivableResponse(
            mercosId: !empty($response->header('MeusPedidosID'))
                ? $response->header('MeusPedidosID')
                : null,
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 201
        );
    }
}
