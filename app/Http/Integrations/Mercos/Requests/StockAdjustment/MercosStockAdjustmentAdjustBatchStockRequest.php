<?php

namespace App\Http\Integrations\Mercos\Requests\StockAdjustment;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosBatchStockAdjustment;
use App\Http\Integrations\Mercos\Requests\MercosBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosStockAdjustmentAdjustBatchStockRequest extends MercosBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosBatchStockAdjustment $mercosBatchStockAdjustment
     */
    public function __construct(protected MercosBatchStockAdjustment $mercosBatchStockAdjustment)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/ajustar_estoque_em_lote';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return $this->mercosBatchStockAdjustment->toArray();
    }
}
