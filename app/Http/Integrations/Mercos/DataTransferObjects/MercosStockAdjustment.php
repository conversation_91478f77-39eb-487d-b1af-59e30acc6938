<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosStockAdjustment
{
    /**
     * Create a new instance.
     *
     * @param  int $produto_id
     * @param  float|null $novo_saldo
     */
    public function __construct(
        public int $produto_id,
        public ?float $novo_saldo
    ) {
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            'produto_id' => $this->produto_id,
            'novo_saldo' => $this->novo_saldo
        ];
    }
}
