<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexSalesman;
use App\Http\Integrations\ErpFlex\ErpFlexConnector;
use App\Http\Integrations\ErpFlex\Requests\Salesman\ErpFlexSalesmanCreateSalesmanRequest;
use App\Http\Integrations\ErpFlex\Requests\Salesman\ErpFlexSalesmanGetSalesmenRequest;
use App\Http\Integrations\ErpFlex\Requests\Salesman\ErpFlexSalesmanUpdateSalesmanRequest;
use Exception;

class ErpFlexSalesmanService extends ErpFlexBaseService
{
    use HandlesLogCreation;

    /**
     * Get all customers.
     *
     * @param  int|null $limit
     * @param  int|null $offset
     * @param  string|null $filters
     * @return mixed
     */
    public function get(?int $limit = null, ?int $offset = null, ?string $filters = null): mixed
    {
        return json_decode(
            (new ErpFlexConnector($this->username, $this->password))
                ->request(new ErpFlexSalesmanGetSalesmenRequest($limit, $offset, $filters))
                ->send()
        );
    }

    /**
     * Creates a new salesman.
     *
     * @param  \App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexSalesman $erpFlexSalesman
     * @return mixed
     */
    public function create(ErpFlexSalesman $erpFlexSalesman): mixed
    {
        $response = (new ErpFlexConnector())
            ->request(new ErpFlexSalesmanCreateSalesmanRequest($erpFlexSalesman))
            ->send();

        $body = json_decode($response->body());

        if (is_null($body)) {
            $this->createLog(
                $response,
                false,
                'erp_flex_salesman_service.create.empty_body'
            );

            throw new Exception('erp_flex_salesman_service.create.empty_body');
        }

        $this->createLog(
            $response,
            ($body->success === 1)
        );

        return $body->data->id ?? null;
    }

    /**
     * Updates a salesman.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexSalesman $erpFlexSalesman
     * @return mixed
     */
    public function update(int $id, ErpFlexSalesman $erpFlexSalesman): mixed
    {
        $response = (new ErpFlexConnector())
            ->request(new ErpFlexSalesmanUpdateSalesmanRequest($id, $erpFlexSalesman))
            ->send();

        $body = json_decode($response->body());

        if (is_null($body)) {
            $this->createLog(
                $response,
                false,
                'erp_flex_salesman_service.update.empty_body'
            );

            throw new Exception('erp_flex_salesman_service.update.empty_body');
        }

        $this->createLog(
            $response,
            ($body->success === 1)
        );

        return $response->ok();
    }
}
