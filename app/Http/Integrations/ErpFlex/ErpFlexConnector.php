<?php

namespace App\Http\Integrations\ErpFlex;

use <PERSON>jo20\Saloon\Http\Auth\BasicAuthenticator;
use Sammyjo20\Saloon\Http\SaloonConnector;
use Sammyjo20\Saloon\Interfaces\AuthenticatorInterface;
use Sammyjo20\Saloon\Traits\Plugins\AcceptsJson;

class <PERSON>rpFlexConnector extends SaloonConnector
{
    use Accepts<PERSON>son;

    /**
     * Create a new instance.
     *
     * @param  string|null $username
     * @param  string|null $password
     */
    public function __construct(
        protected ?string $username = null,
        protected ?string $password = null
    ) {
        $tenantErpFlexApiCredentials = get_erp_flex_api_credentials();

        $this->username ??= $tenantErpFlexApiCredentials['username'];
        $this->password ??= $tenantErpFlexApiCredentials['password'];
    }

    /**
     * The Base URL of the API.
     *
     * @return string
     */
    public function defineBaseUrl(): string
    {
        return config('erp_flex.api.url');
    }

    /**
     * The headers that will be applied to every request.
     *
     * @return string[]
     */
    public function defaultHeaders(): array
    {
        return [];
    }

    /**
     * The config options that will be applied to every request.
     *
     * @return string[]
     */
    public function defaultConfig(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function defaultAuth(): ?AuthenticatorInterface
    {
        return new BasicAuthenticator(
            username: $this->username,
            password: $this->password,
        );
    }
}
