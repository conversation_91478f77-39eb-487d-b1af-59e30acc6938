<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * PriceTable model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $erp_flex_id
 * @property  string $erp_flex_table_id
 * @property  string $mercos_id
 * @property  array $erp_flex_data
 * @property  array $mercos_data
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class PriceTable extends Model
{
    use HasFactory;

    /**
     * @inheritdoc
     */
    protected $table = 'price_tables';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'erp_flex_id',
        'erp_flex_table_id',
        'mercos_id',
        'erp_flex_data',
        'mercos_data'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'erp_flex_data' => 'array',
        'mercos_data' => 'array'
    ];
}
