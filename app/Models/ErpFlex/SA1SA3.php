<?php

namespace App\Models\ErpFlex;

/**
 * SA1_SA3 (customer and salesmen relationship) Model.
 *
 * @package App\Models\ErpFlex;
 *
 * @property  int $SA1_SA3_IDSA1 Customer ID
 * @property  int $SA1_SA3_IDSA3 Salesman ID
 */
class SA1SA3 extends BaseModel
{
    protected $table = 'SA1_SA3';
    protected $primaryKey = 'SA1_SA3_ID';
    protected $prefix = 'SA1_SA3';

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'SA1_SA3_DT_INC' => 'datetime',
        'SA1_SA3_DT_ALT' => 'datetime',
    ];
}
