<?php

use Filament\Notifications\Notification;

/**
 * Notify a default success message.
 *
 * @param  string $body
 * @param  bool $persistent
 * @return Notification|null
 */
function success_notification(string $body, bool $persistent = false): ?Notification
{
    $notification = Notification::make()
        ->title('Sucesso!')
        ->body($body)
        ->success();

    if ($persistent) {
        $notification->persistent();
    } else {
        $notification->seconds(5);
    }

    return $notification;
}

/**
 * Notify a default error message.
 *
 * @param  string|null $body
 * @param  bool $persistent
 * @return Notification|null
 */
function error_notification(?string $body = null, bool $persistent = true): ?Notification
{
    $notification = Notification::make()
        ->title('Ops!')
        ->body($body ?? __('core.errors.general'))
        ->danger();

    if ($persistent) {
        $notification->persistent();
    } else {
        $notification->seconds(5);
    }

    return $notification;
}
