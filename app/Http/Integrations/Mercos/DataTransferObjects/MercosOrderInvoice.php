<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosOrderInvoice
{
    /**
     * Create a new instance.
     *
     * @param  int $cliente_id
     * @param  string $pedido_id
     * @param  string $numero
     * @param  string $serie
     * @param  string $chave_acesso
     * @param  float $valor
     * @param  string $data_emissao
     * @param  string $arquivo_xml
     * @param  string|null $link_xml
     * @param  string|null $arquivo_pdf
     * @param  string|null $link_pdf
     * @param  bool|null $excluido
     */
    public function __construct(
        public int $cliente_id,
        public string $pedido_id,
        public string $numero,
        public string $serie,
        public string $chave_acesso,
        public float $valor,
        public string $data_emissao,
        public string $arquivo_xml,
        public ?string $link_xml = null,
        public ?string $arquivo_pdf = null,
        public ?string $link_pdf = null,
        public ?bool $excluido = null,
    ) {
    }

    /**
     * Create a new instance from an object.
     *
     * @param  object $object
     * @return static
     */
    public static function fromObject(object $object): static
    {
        return new static(
            cliente_id: $object->cliente_id,
            pedido_id: $object->pedido_id,
            numero: $object->numero,
            serie: $object->serie,
            chave_acesso: $object->chave_acesso,
            valor: $object->valor,
            data_emissao: $object->data_emissao,
            arquivo_xml: $object->arquivo_xml,
            link_xml: $object->link_xml,
            arquivo_pdf: $object->arquivo_pdf,
            link_pdf: $object->link_pdf,
            excluido: $object->excluido
        );
    }
}
