<?php

namespace App\Filament\Resources\CustomerResource\Concerns;

use App\Models\Customer;
use Filament\Tables\Columns\TextColumn;

trait HandlesCustomerResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')->label('ID'),
            TextColumn::make('erp_flex_id')->label('ID ERPFlex'),
            TextColumn::make('mercos_id')->label('ID Mercos'),
            TextColumn::make('name')
                ->label('Razão social')
                ->formatStateUsing(fn (Customer $record): string => $record->erp_flex_data['SA1_Desc']),
            TextColumn::make('trading_name')
                ->label('Nome fantasia')
                ->formatStateUsing(fn (Customer $record): string => $record->erp_flex_data['SA1_Fantasia']),
            TextColumn::make('tax_id_number')
                ->label('CPF/CNPJ')
                ->formatStateUsing(
                    fn (Customer $record): string => isset($record->erp_flex_data['SA1_CPF']) && strlen($record->erp_flex_data['SA1_CPF']) === 11
                        ? mask_cpf($record->erp_flex_data['SA1_CPF'])
                        : mask_cnpj($record->erp_flex_data['SA1_CPF'])
                ),
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [];
    }
}
