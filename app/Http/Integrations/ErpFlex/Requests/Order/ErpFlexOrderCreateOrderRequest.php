<?php

namespace App\Http\Integrations\ErpFlex\Requests\Order;

use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexOrder;
use App\Http\Integrations\ErpFlex\Requests\ErpFlexBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class ErpFlexOrderCreateOrderRequest extends ErpFlexBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexOrder $erpFlexOrder
     */
    public function __construct(protected ErpFlexOrder $erpFlexOrder)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/api/orcamento';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return $this->erpFlexOrder->toArray(true);
    }
}
