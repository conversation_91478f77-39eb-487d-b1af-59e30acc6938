<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Invoice model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $order_id
 * @property  string $erp_flex_invoice_id
 * @property  string $mercos_invoice_id
 * @property  array $erp_flex_invoice_data
 * @property  array $mercos_invoice_data
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class Invoice extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'order_id',
        'erp_flex_invoice_id',
        'mercos_invoice_id',
        'erp_flex_invoice_data',
        'mercos_invoice_data',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'erp_flex_invoice_data' => 'array',
        'mercos_invoice_data' => 'array',
    ];
}
