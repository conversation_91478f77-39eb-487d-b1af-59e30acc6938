<?php

namespace App\Filament\Resources\HaulierResource\Pages;

use App\Console\Commands\SyncHauliers;
use App\Filament\Resources\HaulierResource;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Support\Facades\Artisan;
use Throwable;

class ManageHauliers extends ManageRecords
{
    protected static string $resource = HaulierResource::class;

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            Action::make('Mapear transportadoras')
                ->visible(!integration_settings()->hauliers_pairing_finished)
                ->url(route('hauliers.pairing'))
                ->icon('heroicon-o-cog')
                ->color('secondary'),
            Action::make('Forçar sincronização')
                ->visible(integration_settings()->hauliers_pairing_finished)
                ->action(function (): void {
                    try {
                        Artisan::call(SyncHauliers::class, ['force' => true]);
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->color('warning')
                ->requiresConfirmation(),
            Action::make('Sincronizar')
                ->visible(integration_settings()->hauliers_pairing_finished)
                ->action(function (): void {
                    try {
                        Artisan::call(SyncHauliers::class);
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->requiresConfirmation(),
        ];
    }
}
