<?php

namespace App\Filament\Resources\CategoryResource\Pages;

use App\Actions\Mercos\CreateCategoriesFromErpFlex;
use App\Filament\Resources\CategoryResource;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ManageRecords;
use Throwable;

class ManageCategories extends ManageRecords
{
    protected static string $resource = CategoryResource::class;

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            Action::make('Mapear categorias')
                ->visible(!integration_settings()->categories_pairing_finished)
                ->url(route('categories.pairing'))
                ->icon('heroicon-o-cog')
                ->color('secondary'),
            Action::make('Forçar sincronização')
                ->visible(integration_settings()->categories_pairing_finished)
                ->action(function (): void {
                    try {
                        CreateCategoriesFromErpFlex::dispatch(true);
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->color('warning')
                ->requiresConfirmation(),
            Action::make('Sincronizar')
                ->visible(integration_settings()->categories_pairing_finished)
                ->action(function (): void {
                    try {
                        CreateCategoriesFromErpFlex::dispatch();
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->requiresConfirmation(),
        ];
    }
}
