<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\TenantResource\Pages;
use App\Filament\Admin\Resources\TenantResource\RelationManagers;
use App\Models\Tenant;
use Filament\Forms;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TenantResource extends Resource
{
    protected static ?string $model = Tenant::class;
    protected static ?string $modelLabel = 'base';
    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(1)->schema([
                    Tabs::make('tabs')->schema([
                        Tab::make('Identificação')->schema([
                            Grid::make(1)->schema([
                                TextInput::make('id')
                                    ->label('ID')
                                    ->disabledOn('edit'),
                            ]),
                        ]),
                        Tab::make('ERPFlex')->schema([
                            Grid::make(2)->schema([
                                Select::make('erp_flex_connection_mode')
                                    ->label('Modo de conexão')
                                    ->selectablePlaceholder(false)
                                    ->options([
                                        'sandbox' => 'Homologação',
                                        'production' => 'Produção',
                                    ]),
                                TextInput::make('erp_flex_tenant_id')
                                    ->label('ID EA1'),
                            ]),
                            Grid::make(2)->schema([
                                TextInput::make('erp_flex_api_username')
                                    ->label('Usuário API'),
                                TextInput::make('erp_flex_api_password')
                                    ->label('Senha API'),
                            ]),
                            Grid::make(1)->schema([
                                Toggle::make('erp_flex_multi_database')
                                    ->label('Utiliza base múltipla?'),
                            ]),
                        ]),
                        Tab::make('Mercos')->schema([
                            Grid::make(2)->schema([
                                Select::make('mercos_connection_mode')
                                    ->label('Modo de conexão')
                                    ->selectablePlaceholder(false)
                                    ->options([
                                        'sandbox' => 'Homologação',
                                        'production' => 'Produção',
                                    ]),
                                TextInput::make('mercos_api_company_token')
                                    ->label('Company token'),
                            ]),
                        ]),
                    ]),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID'),
                IconColumn::make('is_multi_database')
                    ->label('Utiliza base múltipla?')
                    ->boolean()
                    ->getStateUsing(fn (Tenant $tenant): bool => $tenant->erp_flex['multi_database']),
            ])
            ->filters([
                TernaryFilter::make('multi_database')
                    ->label('Utiliza base múltipla?')
                    ->placeholder('Todos')
                    ->trueLabel('Sim')
                    ->falseLabel('Não')
                    ->queries(
                        true: fn (Builder $query): Builder => $query->whereRaw('JSON_EXTRACT(data, "$.erp_flex.multi_database") = true'),
                        false: fn (Builder $query): Builder => $query->whereRaw('JSON_EXTRACT(data, "$.erp_flex.multi_database") = false'),
                        blank: fn (Builder $query): Builder => $query
                    )
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTenants::route('/'),
            'create' => Pages\CreateTenant::route('/create'),
            'edit' => Pages\EditTenant::route('/{record}/edit'),
            'view' => Pages\ViewTenant::route('/{record}'),
        ];
    }
}
