<?php

namespace App\Http\Integrations\ErpFlex\Services;

class ErpFlexBaseService
{
    /**
     * Create a new instance.
     *
     * @param  string|null $username
     * @param  string|null $password
     */
    public function __construct(
        protected ?string $username = null,
        protected ?string $password = null,
    ) {
        $tenantErpFlexApiCredentials = get_erp_flex_api_credentials();

        $this->username ??= $tenantErpFlexApiCredentials['username'];
        $this->password ??= $tenantErpFlexApiCredentials['password'];
    }

    /**
     * Build an instance.
     *
     * @param  string|null $username
     * @param  string|null $password
     * @return static
     */
    public static function make(?string $username = null, ?string $password = null): static
    {
        return new static($username, $password);
    }
}
