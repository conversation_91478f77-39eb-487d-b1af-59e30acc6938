<?php

namespace App\Http\Integrations\Mercos\Services;

class MercosBaseService
{
    /**
     * How long a transaction should wait between
     * transactions (in seconds).
     */
    protected const DELAY_FOR_RATE_LIMITING = 1;

    /**
     * Create a new instance.
     *
     * @param  string|null $applicationToken
     * @param  string|null $companyToken
     */
    public function __construct(
        protected ?string $applicationToken = null,
        protected ?string $companyToken = null,
    ) {
        $mercosTokens = get_mercos_tokens();

        $this->applicationToken ??= $mercosTokens['application'];
        $this->companyToken ??= $mercosTokens['company'];
    }

    /**
     * Build an instance.
     *
     * @param  string|null $applicationToken
     * @param  string|null $companyToken
     * @return static
     */
    public static function make(?string $applicationToken = null, ?string $companyToken = null): static
    {
        return new static($applicationToken, $companyToken);
    }
}
