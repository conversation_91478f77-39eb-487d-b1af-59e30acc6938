<?php

namespace App\Models;

use App\Models\Concerns\Subcategory\HandlesSubcategoryRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Subcategory model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $category_id
 * @property  string $erp_flex_id
 * @property  string $mercos_id
 * @property  array $erp_flex_data
 * @property  array $mercos_data
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Category $category
 */
class Subcategory extends Model
{
    use HandlesSubcategoryRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'category_id',
        'erp_flex_id',
        'mercos_id',
        'erp_flex_data',
        'mercos_data'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'erp_flex_data' => 'array',
        'mercos_data' => 'array'
    ];
}
