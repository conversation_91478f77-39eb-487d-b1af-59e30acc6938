<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosSinglePriceTableProduct
{
    /**
     * Create a new instance.
     *
     * @param  int $produto_id
     * @param  int $tabela_id
     * @param  float $preco
     * @param  int|null $id
     */
    public function __construct(
        public int $produto_id,
        public int $tabela_id,
        public ?float $preco,
        public ?int $id = null
    ) {
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        $data = [
            'produto_id' => $this->produto_id,
            'tabela_id' => $this->tabela_id,
            'preco' => $this->preco,
        ];

        if (!is_null($this->id)) {
            $data['id'] = $this->id;
        }

        return $data;
    }
}
