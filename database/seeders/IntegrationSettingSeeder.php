<?php

namespace Database\Seeders;

use App\Models\IntegrationSetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class IntegrationSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        IntegrationSetting::create([
            'enables_customer_automation' => false,
            'enables_customer_mercos_to_erp_flex_flow' => false,
            'enables_products_automation' => false,
            'enables_price_table_automation' => false,
            'enables_stock_automation' => false,
            'enables_orders_automation' => false,
            'enables_payment_conditions_automation' => false,
            'enables_salesmen_automation' => false,
            'enables_hauliers_automation' => false,
            'customers_pairing_finished' => false,
            'categories_pairing_finished' => false,
            'subcategories_pairing_finished' => false,
            'products_pairing_finished' => false,
            'payment_conditions_pairing_finished' => false,
            'hauliers_pairing_finished' => false,
        ]);
    }
}
