<?php

namespace App\Actions\Mercos;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosCustomerXSalesman;
use App\Http\Integrations\Mercos\Services\MercosCustomerXSalesmanService;
use App\Models\Customer;
use App\Models\CustomerXSaleman;
use App\Models\ErpFlex\SA1SA3;
use App\Models\Salesman;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateCustomersXSalesmenLinkFromErpFlex
{
    use AsAction;

    protected MercosCustomerXSalesmanService $mercosCustomerXSalesmanService;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue('erp_flex_management_' . tenant('id'));
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (
            !integration_settings()->enables_customer_automation
            || !integration_settings()->enables_salesmen_automation
        ) {
            return;
        }

        if (!integration_settings()->customers_pairing_finished) {
            return;
        }

        $this->mercosCustomerXSalesmanService = new MercosCustomerXSalesmanService();

        $this->integrateLink();
    }

    /**
     * Send ErpFlex's customers and salesmen link.
     *
     * @return void
     */
    protected function integrateLink(): void
    {
        try {
            SA1SA3::query()
                ->lazyById(1000)
                ->each(function (mixed $erpFlexLink): void {
                    $this->processSingleCustomer($erpFlexLink);
                });
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Process a single customer.
     *
     * @param  \App\Models\ErpFlex\SA1SA3 $erpFlexLink
     * @return void
     */
    protected function processSingleCustomer(SA1SA3 $erpFlexLink): void
    {
        /** @var \App\Models\Customer $customer */
        $customer = Customer::query()
            ->where('erp_flex_id', $erpFlexLink->SA1_SA3_IDSA1)
            ->first();

        /**
         * @var \App\Models\Salesman $salesman
         */
        $salesman = Salesman::query()
            ->where('erp_flex_id', $erpFlexLink->SA1_SA3_IDSA3)
            ->whereJsonContains('mercos_data->excluido', false)
            ->first();

        if (is_null($customer) || is_null($salesman)) {
            return;
        }

        $exists = CustomerXSaleman::query()
            ->where('customer_id', $customer->id)
            ->where('salesman_id', $salesman->id)
            ->exists();

        if ($exists) {
            return;
        }

        $link = CustomerXSaleman::create([
            'customer_id' => $customer->id,
            'salesman_id' => $salesman->id,
            'erp_flex_customer_id' => $customer->erp_flex_id,
            'erp_flex_salesman_id' => $salesman->erp_flex_id,
            'mercos_customer_id' =>  $customer->mercos_id,
            'mercos_salesman_id' => $salesman->mercos_id,
            'active' => true,
        ]);

        $this->createLink($link, $erpFlexLink, $customer, $salesman);
    }

    protected function createLink(CustomerXSaleman $link)
    {
        $mercosCustomerObject = new MercosCustomerXSalesman(
            cliente_id: (int) $link->mercos_customer_id,
            usuario_id: (int) $link->mercos_salesman_id,
            liberado: true
        );

        $response = $this->mercosCustomerXSalesmanService->create($mercosCustomerObject);

        sleep($response->delayForRateLimiting);

        if (!$response->success) {
            $response = $this->mercosCustomerXSalesmanService->create($mercosCustomerObject);
        }
    }
}
