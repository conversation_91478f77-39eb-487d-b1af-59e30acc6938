<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Concerns\HandlesProductResourceForm;
use App\Filament\Resources\ProductResource\Concerns\HandlesProductResourceTable;
use App\Filament\Resources\ProductResource\Pages\ManageProducts;
use App\Models\Product;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

class ProductResource extends Resource
{
    use HandlesProductResourceForm;
    use HandlesProductResourceTable;

    protected static ?string $model = Product::class;
    protected static ?string $modelLabel = 'produto';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Configure the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => ManageProducts::route('/'),
        ];
    }
}
