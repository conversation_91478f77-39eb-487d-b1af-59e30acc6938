<?php

namespace App\Filament\Resources;

use App\Filament\Resources\IntegrationSettingResource\Concerns\HandlesIntegrationSettingResourceForm;
use App\Filament\Resources\IntegrationSettingResource\Concerns\HandlesIntegrationSettingResourceTable;
use App\Filament\Resources\IntegrationSettingResource\Pages\ManageIntegrationSettings;
use App\Models\IntegrationSetting;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;

class IntegrationSettingResource extends Resource
{
    use HandlesIntegrationSettingResourceForm;
    use HandlesIntegrationSettingResourceTable;

    protected static ?string $model = IntegrationSetting::class;
    protected static ?string $modelLabel = 'configuração de integração';
    protected static ?string $pluralModelLabel = 'configurações de integração';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    /**
     * Configure the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => ManageIntegrationSettings::route('/'),
        ];
    }
}
