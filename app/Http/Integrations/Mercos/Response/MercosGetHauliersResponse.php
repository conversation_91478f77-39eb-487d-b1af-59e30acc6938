<?php

namespace App\Http\Integrations\Mercos\Response;

use stdClass;

class MercosGetHauliersResponse
{
     /**
      * Create a new instance.
      *
      * @param array $content
      * @param integer|null $delayForRateLimiting
      * @param integer $extrasRequests
      * @param boolean|null $success
      */
    public function __construct(
        public array $content = [],
        public ?int $delayForRateLimiting = null,
        public ?int $extrasRequests = 0,
        public ?bool $success = null
    ) {
    }
}
