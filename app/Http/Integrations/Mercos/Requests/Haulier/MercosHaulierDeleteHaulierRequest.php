<?php

namespace App\Http\Integrations\Mercos\Requests\Haulier;

use App\Http\Integrations\Mercos\Requests\MercosBasePutRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosHaulierDeleteHaulierRequest extends MercosBasePutRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  int $id
     * @param  string $name
     */
    public function __construct(protected int $id, protected string $name)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/transportadoras/' . $this->id;
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return [
            'nome' => $this->name,
            'excluido' => true
        ];
    }
}
