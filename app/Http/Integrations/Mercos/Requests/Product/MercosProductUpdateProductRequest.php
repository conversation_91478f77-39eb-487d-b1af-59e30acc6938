<?php

namespace App\Http\Integrations\Mercos\Requests\Product;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosProduct;
use App\Http\Integrations\Mercos\Requests\MercosBasePutRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosProductUpdateProductRequest extends MercosBasePutRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosProduct $mercosProduct
     */
    public function __construct(
        protected int $id,
        protected MercosProduct $mercosProduct
    ) {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/produtos/' . $this->id;
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        $data = array_filter([
            'codigo' => $this->mercosProduct->codigo,
            'nome' => $this->mercosProduct->nome,
            'comissao' => $this->mercosProduct->comissao,
            'preco_tabela' => $this->mercosProduct->preco_tabela,
            'preco_minimo' => $this->mercosProduct->preco_minimo,
            'ipi' => $this->mercosProduct->ipi,
            'tipo_ipi' => $this->mercosProduct->tipo_ipi,
            'st' => $this->mercosProduct->st,
            'grade_cores' => $this->mercosProduct->grade_cores,
            'grade_tamanhos' => $this->mercosProduct->grade_tamanhos,
            'moeda' => $this->mercosProduct->moeda,
            'unidade' => $this->mercosProduct->unidade,
            'saldo_estoque' => $this->mercosProduct->saldo_estoque,
            'observacoes' => $this->mercosProduct->observacoes,
            'excluido' => $this->mercosProduct->excluido,
            'ativo' => $this->mercosProduct->ativo,
            'categoria_id' => $this->mercosProduct->categoria_id,
            'codigo_ncm' => $this->mercosProduct->codigo_ncm,
            'multiplo' => $this->mercosProduct->multiplo,
            'peso_bruto' => $this->mercosProduct->peso_bruto,
            'largura' => $this->mercosProduct->largura,
            'altura' => $this->mercosProduct->altura,
            'comprimento' => $this->mercosProduct->comprimento,
            'peso_dimensoes_unitario' => $this->mercosProduct->peso_dimensoes_unitario,
            'exibir_no_b2b' => $this->mercosProduct->exibir_no_b2b
        ]);

        $data['ativo'] = $this->mercosProduct->ativo;

        return $data;
    }
}
