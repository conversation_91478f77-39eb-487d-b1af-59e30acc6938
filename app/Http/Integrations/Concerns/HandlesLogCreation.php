<?php

namespace App\Http\Integrations\Concerns;

use App\Models\Log;
use Illuminate\Support\Facades\Log as FacadesLog;
use Sammyjo20\Saloon\Http\SaloonResponse;
use Throwable;

trait HandlesLogCreation
{
    /**
     * Creates the request log.
     *
     * @param  SaloonResponse $response
     * @param  boolean|null $success
     * @param  string|null $errorDescription
     * @param  array|null $requestBody
     * @return Log
     */
    protected function createLog(
        SaloonResponse $response,
        ?bool $success = null,
        ?string $errorDescription = null,
        array $requestBody = null
    ): Log {
        try {
            $request = $response->getOriginalRequest();

            if (is_null($success)) {
                $success = $response->successful();
            }

            $bt = debug_backtrace();

            return Log::create([
                'service_name' => self::class,
                'service_method' => $bt[1]['function'] ?? null,
                'success' => $success,
                'status_code' => $response->status(),
                'response_headers' => $response->headers(),
                'response_body' => is_null(json_decode($response->body(), true))
                    ? ['data' => $response->body()]
                    : json_decode($response->body(), true),
                'method' => $request->getMethod(),
                'url' => $request->getFullRequestUrl(),
                'request_headers' => [
                    'data' =>
                    base_encrypt(json_encode($request->getHeaders(), true))
                ],
                'request_body' => $requestBody,
                'error_description' => $errorDescription,
            ]);
        } catch (Throwable $th) {
            FacadesLog::error($th);
        }
    }
}
