<?php

declare(strict_types=1);

use App\Filament\Pages\Category\CategoryPairing;
use App\Filament\Pages\Customer\CustomerPairing;
use App\Filament\Pages\Customer\CustomerPairingMercos;
use App\Filament\Pages\Haulier\HaulierPairing;
use App\Filament\Pages\PaymentCondition\PaymentConditionPairing;
use App\Filament\Pages\Product\ProductPairing;
use App\Filament\Pages\Subcategory\SubcategoryPairing;
use App\Filament\Pages\UpdatePassword;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {
    Route::get('/', function () {
        return 'This is your multi-tenant application. The id of the current tenant is ' . tenant('id');
    });

    Route::get('app/update-password', UpdatePassword::class)->name('filament.pages.update-password');
    Route::get('app/customers/pairing', CustomerPairing::class)->name('customers.pairing');
    Route::get('app/customers/pairing-mercos', CustomerPairingMercos::class)->name('customers.pairing-mercos');
    Route::get('app/products/pairing', ProductPairing::class)->name('products.pairing');
    Route::get('app/categories/pairing', CategoryPairing::class)->name('categories.pairing');
    Route::get('app/subcategories/pairing', SubcategoryPairing::class)->name('subcategories.pairing');
    Route::get('app/payment-conditions/pairing', PaymentConditionPairing::class)->name('payment-conditions.pairing');
    Route::get('app/hauliers/pairing', HaulierPairing::class)->name('hauliers.pairing');
});
