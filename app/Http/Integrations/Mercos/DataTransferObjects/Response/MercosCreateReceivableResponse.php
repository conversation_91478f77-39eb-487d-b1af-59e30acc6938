<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects\Response;

class MercosCreateReceivableResponse
{
    /**
     * Create a new instance.
     *
     * @param  string|null $mercosId
     * @param  int|null $delayForRateLimiting
     * @param  int|null $success
     */
    public function __construct(
        public ?string $mercosId = null,
        public ?int $delayForRateLimiting = null,
        public ?bool $success = null
    ) {
    }
}
