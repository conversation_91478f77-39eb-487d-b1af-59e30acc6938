<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers_pairing', function (Blueprint $table) {
            $table->id();
            $table->string('erp_flex_id')->nullable();
            $table->string('mercos_id')->nullable();
            $table->json('erp_flex_data')->nullable();
            $table->json('mercos_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers_pairing');
    }
};
