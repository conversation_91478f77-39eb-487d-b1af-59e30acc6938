<?php

namespace App\Actions\ErpFlex;

use App\Models\ErpFlex\SA1;
use App\Models\IntegrationSetting;
use App\Models\MercosCustomer;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateMercosCustomersFromErpFlex
{
    use AsAction;

    protected bool $force = false;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(tenant('id') . '_cadastros');
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_customer_automation) {
            return;
        }

        $this->force = $force;

        $this->integrateCustomers();
    }

    /**
     * Integrate all customers from ERPFlex to Mercos.
     *
     * @return void
     */
    protected function integrateCustomers(): void
    {
        try {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();
            $executionStart = now();

            $updateStartedAt = Carbon::parse($integrationSetting->last_customers_update)
                ->setTimezone('America/Sao_Paulo');

            SA1::query()
                ->when(!$this->force, function (Builder $query) use ($updateStartedAt) : Builder {
                    return $query->where(function (Builder $query) use ($updateStartedAt) : Builder {
                        return $query
                            ->where('SA1_DT_INC', '>=', $updateStartedAt)
                            ->orWhere('SA1_DT_ALT', '>=', $updateStartedAt);
                    });
                })
                ->lazyById(1000)
                ->each(function (mixed $erpFlexApiCustomer): void {
                    $this->processSingleCustomer($erpFlexApiCustomer);
                });

            $integrationSetting->update(['last_customers_update' => $executionStart]);
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Process a single customer.
     *
     * @param  \App\Models\ErpFlex\SA1 $erpFlexApiCustomer
     * @return void
     */
    protected function processSingleCustomer(SA1 $erpFlexApiCustomer): void
    {
        $erpFlexData = [
            'SA1_Desc' => $erpFlexApiCustomer->SA1_Desc ?? '',
            'SA1_Fantasia' => $erpFlexApiCustomer->SA1_Fantasia ?? '',
            'SA1_CPF' => $erpFlexApiCustomer->SA1_CPF ?? '',
            'SA1_Inscr' => $erpFlexApiCustomer->SA1_Inscr ?? '',
            'SA1_SUFRAMA' => $erpFlexApiCustomer->SA1_SUFRAMA ?? '',
            'SA1_End' => $erpFlexApiCustomer->SA1_End ?? '',
            'SA1_Numero' => $erpFlexApiCustomer->SA1_Numero ?? '',
            'SA1_Complemento' => $erpFlexApiCustomer->SA1_Complemento ?? '',
            'SA1_CEP' => $erpFlexApiCustomer->SA1_CEP ?? '',
            'SA1_Bairro' => $erpFlexApiCustomer->SA1_Bairro ?? '',
            'SA1_Mun' => $erpFlexApiCustomer->SA1_Mun ?? '',
            'SA1_Est' => $erpFlexApiCustomer->SA1_Est ?? '',
            'SA1_EMail' => $erpFlexApiCustomer->SA1_EMail ?? '',
            'SA1_EMailLOJA' => $erpFlexApiCustomer->SA1_EMailLOJA ?? '',
            'SA1_EMailDANFE' => $erpFlexApiCustomer->SA1_EMailDANFE ?? '',
            'SA1_EMailCobranca' => $erpFlexApiCustomer->SA1_EMailCobranca ?? '',
            'SA1_DDDCom' => $erpFlexApiCustomer->SA1_DDDCom ?? '',
            'SA1_TelCom' => $erpFlexApiCustomer->SA1_TelCom ?? '',
            'SA1_DDDRes' => $erpFlexApiCustomer->SA1_DDDRes ?? '',
            'SA1_TelRes' => $erpFlexApiCustomer->SA1_TelRes ?? '',
            'SA1_DDDCel' => $erpFlexApiCustomer->SA1_DDDCel ?? '',
            'SA1_TelCel' => $erpFlexApiCustomer->SA1_TelCel ?? '',
        ];

        MercosCustomer::updateOrCreate([
            'erp_flex_id' => $erpFlexApiCustomer->SA1_ID
        ], [
            'erp_flex_data' => $erpFlexData
        ]);
    }
}
