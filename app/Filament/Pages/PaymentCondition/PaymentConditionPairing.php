<?php

namespace App\Filament\Pages\PaymentCondition;

use App\Actions\Mercos\CreatePaymentConditionsFromErpFlex;
use App\Actions\Mercos\CreateMercosPaymentConditionsFromMercos;
use App\Actions\Pairing\PaymentConditionPairing as PaymentConditionPairingAction;
use App\Filament\Pages\Concerns\HandlesPairingImport;
use App\Filament\Resources\PaymentConditionResource;
use App\Models\MercosPaymentCondition;
use App\Models\PaymentConditionPairing as PaymentConditionPairingModel;
use Awcodes\FilamentTableRepeater\Components\TableRepeater;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Collection;
use Throwable;

class PaymentConditionPairing extends ListRecords
{
    use InteractsWithForms;
    use HandlesPairingImport;

    protected static ?string $title = 'Mapeamento de Formas de Pagamento';
    protected static string $view = 'filament.pages.payment-conditions.payment-condition-pairing';
    protected static string $resource = PaymentConditionResource::class;

    protected string $routeName = 'payment-conditions.pairing';
    protected bool $mercosOriginFlow = false;

    /**
     * @var \Illuminate\Support\Collection
     */
    public $erpFlexPaymentConditionsCollection;

    public array $erpFlexPaymentConditions;

    /**
     * @var \Illuminate\Support\Collection
     */
    public $mercosPaymentConditions;

    /**
     * @var \Illuminate\Support\Collection
     */
    public Collection $erpFlexOptions;

    /**
     * @var \Illuminate\Support\Collection
     */
    public  Collection $mercosOptions;

    /** @inheritDoc */
    public function mount(): void
    {
        $this->setPaymentConditionData();
    }

    /** @inheritDoc */
    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return false;
    }

    /**
     * Sets the payment conditions pairing data
     *
     * @return void
     */
    protected function setPaymentConditionData()
    {
        $this->erpFlexPaymentConditionsCollection = PaymentConditionPairingModel::get();
        $this->erpFlexPaymentConditions = $this->erpFlexPaymentConditionsCollection->toArray();
        $this->mercosPaymentConditions = MercosPaymentCondition::get();

        $this->erpFlexOptions = $this->erpFlexPaymentConditionsCollection->mapWithKeys(function ($item) {
            $name = trim(
                ($item->erp_flex_data['SEP_Desc'] ?? '') . ' ' . ($item->erp_flex_data['SEP_Fantasia'] ?? '')
            );

            return [
                $item->erp_flex_id => $name,
            ];
        });

        $this->mercosOptions = $this->mercosPaymentConditions->mapWithKeys(function ($item) {
            return [
                $item->mercos_id => !empty($item->mercos_data['nome'])
                    ? $item->mercos_data['nome']
                    : 'Sem nome'
            ];
        });
    }

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            Action::make('Aprovar mapeamento')
                ->action(function (): void {
                    try {
                        $this->onApproveMapping();
                        success_notification('O mapeamento foi realizado com sucesso')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-check')
                ->color('warning')
                ->requiresConfirmation(),
            $this->defineImportAction($this->erpFlexPaymentConditionsCollection, $this->mercosPaymentConditions, new PaymentConditionPairingModel()),
            Action::make('Salvar rascunho')
                ->action(function (): void {
                    try {
                        $this->onSaveMapping();
                        success_notification('O rascunho foi salvo com sucesso')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->color('success')
                ->icon('heroicon-s-save'),
            Action::make('Sincronizar dados')
                ->action(function (): void {
                    try {
                        $this->onRefreshData();
                        success_notification('Os dados foram atualizados')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->color('success')
                ->icon('heroicon-s-arrow-path')
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            Section::make('Mapear formas de pagamento')
                ->compact()
                ->schema([
                    Grid::make(1)->schema([
                        TableRepeater::make('erpFlexPaymentConditions')
                            ->disableItemCreation()
                            ->disableItemMovement()
                            ->disableItemDeletion()
                            ->disableLabel()
                            ->label('')
                            ->columns(2)
                            ->schema([
                                Select::make('erp_flex_id')
                                    ->disabled()
                                    ->disableLabel()
                                    ->label('Forma de Pagamento ErpFlex (um ID por linha)')
                                    ->options($this->erpFlexOptions),
                                Select::make('mercos_id')
                                    ->disableLabel()
                                    ->label('Forma de Pagamento Mercos (um ID por linha)')
                                    ->options($this->mercosOptions)
                                    ->searchable()
                            ])
                    ])
                ]),
        ];
    }

    /**
     * Saves the current mapping collection
     *
     * @return void
     */
    public function onSaveMapping(): void
    {
        foreach ($this->erpFlexPaymentConditions as $paymentCondition) {
            $mercosData = null;

            if (!empty($paymentCondition['mercos_id'])) {
                $mercosData = $this->mercosPaymentConditions
                    ->where('mercos_id', $paymentCondition['mercos_id'])
                    ->first()?->mercos_data;
            }

            PaymentConditionPairingModel::updateOrCreate(
                ['id' => $paymentCondition['id']],
                [
                    'mercos_id' => $paymentCondition['mercos_id'] ?? null,
                    'mercos_data' => $mercosData
                ]
            );
        }
    }

    /**
     * Transforms the current mapping into the proper models.
     *
     * @return mixed
     */
    public function onApproveMapping()
    {
        $this->onSaveMapping();
        PaymentConditionPairingAction::run();

        return redirect()->to('/app/payment-conditions');
    }

    /**
     * Refreshes the data on both sides.
     *
     * @return mixed
     */
    public function onRefreshData()
    {
        CreatePaymentConditionsFromErpFlex::run(true);
        CreateMercosPaymentConditionsFromMercos::run(true);

        return redirect()->to(route('payment-conditions.pairing'));
    }
}
