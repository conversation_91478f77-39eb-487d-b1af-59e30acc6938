<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects\Response;

class MercosBatchUpdatePriceTableProductsResponse
{
    /**
     * Create a new instance
     *
     * @param  int|null $delayForRateLimiting
     * @param  int|null $success
     */
    public function __construct(
        public ?array $data = null,
        public ?int $delayForRateLimiting = null,
        public ?bool $success = null
    ) {
    }
}
