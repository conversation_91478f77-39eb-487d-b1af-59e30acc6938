<?php

namespace App\Actions\Mercos;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosBatchStockAdjustment;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosStockAdjustment;
use App\Http\Integrations\Mercos\Services\MercosStockAdjustmentService;
use App\Models\ErpFlex\SB2;
use App\Models\IntegrationSetting;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class UpdateProductsStockFromErpFlex
{
    use AsAction;

    protected bool $force = false;

    protected MercosStockAdjustmentService $mercosStockAdjustmentService;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue('erp_flex_stock_' . tenant('id'));
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @param  int|null $erpFlexId
     * @return void
     */
    public function handle(bool $force = false, ?int $erpFlexId = null): void
    {
        $integrationSettings = integration_settings();

        if (
            !$integrationSettings->enables_products_automation
            || !$integrationSettings->enables_stock_automation
            || !integration_settings()->products_pairing_finished
        ) {
            return;
        }

        $this->force = $force;
        $this->mercosStockAdjustmentService = new MercosStockAdjustmentService();

        $this->integrateStock($erpFlexId);
    }

    /**
     * Integrate the stock information.
     *
     * @param  int|null $erpFlexId
     * @return void
     */
    protected function integrateStock(?int $erpFlexId = null): void
    {
        /** @var \App\Models\IntegrationSetting $integrationSetting */
        $integrationSetting = IntegrationSetting::first();
        $executionStart = now();

        $updateStartedAt = $integrationSetting->last_erp_flex_stock_update
            ? Carbon::parse($integrationSetting->last_erp_flex_stock_update)->setTimezone('America/Sao_Paulo')
            : Carbon::parse('1900-01-01');

        Product::query()->update(['stock_updated' => false]);

        Product::query()
            ->select(['erp_flex_id'])
            ->whereNotNull('mercos_id')
            ->get()
            ->pluck('erp_flex_id')
            ->chunk(250)
            ->each(function (Collection $productIdChunk) use ($erpFlexId, $updateStartedAt, $integrationSetting): void {
                $this->processGroup($productIdChunk, $erpFlexId, $updateStartedAt, $integrationSetting);
            });

        if (!$erpFlexId) {
            Product::query()
                ->select(['erp_flex_id'])
                ->where('stock_updated', false)
                ->whereNotNull('mercos_id')
                ->get()
                ->pluck('erp_flex_id')
                ->chunk(250)
                ->each(function (Collection $productIdChunk) use ($erpFlexId, $updateStartedAt, $integrationSetting): void {
                    $this->processGroup($productIdChunk, $erpFlexId, $updateStartedAt, $integrationSetting);
                });
        }

        $integrationSetting->update(['last_erp_flex_stock_update' => $executionStart]);
    }

    /**
     * Process the group.
     *
     * @param  \Illuminate\Support\Collection $productIdChunk
     * @param  int|null $erpFlexId
     * @param  \Carbon\Carbon $updateStartedAt
     * @param  \App\Models\IntegrationSetting $integrationSetting
     * @return void
     */
    protected function processGroup(
        Collection $productIdChunk,
        ?int $erpFlexId,
        Carbon $updateStartedAt,
        IntegrationSetting $integrationSetting
    ): void {
        $sb2Group = SB2::query()
            ->with('SB1:SB1_ID,SB1_Tipo,SB1_AutomComercial,SB1_Ativo')
            ->select(['SB2_ID', 'SB2_IDSB1', 'SB2_DT_INC', 'SB2_DT_ALT', 'SB2_DT_ALT_EST', 'SB2_QAtu', 'SB2_Reserva'])
            ->whereHas('SB1', function (Builder $query): Builder {
                return $query
                    ->whereIn('SB1_Tipo', ['PA', 'RV'])
                    ->where('SB1_AutomComercial', 'S')
                    ->where('SB1_Ativo', true);
            })
            ->when($erpFlexId, fn (Builder $query): Builder => $query->where('SB2_ID', $erpFlexId))
            ->when(!$erpFlexId, fn (Builder $query): Builder => $query->whereIn('SB2_ID', $productIdChunk->toArray()))
            ->when(!$this->force, function (Builder $query) use ($updateStartedAt): Builder {
                return $query
                    ->where('SB2_DT_INC', '>=', $updateStartedAt)
                    ->orWhere('SB2_DT_ALT', '>=', $updateStartedAt)
                    ->orWhere('SB2_DT_ALT_EST', '>=', $updateStartedAt);
            })
            ->get();

        try {
            $this->realizeBatchAdjustment($sb2Group, $integrationSetting);

            Product::query()
                ->whereIn('erp_flex_id', $productIdChunk->toArray())
                ->update(['stock_updated' => true]);
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Realize the batch adjustment for the collection.
     *
     * @param  \Illuminate\Support\Collection $sb2Group
     * @param  \App\Models\IntegrationSetting $integrationSetting
     * @return void
     */
    protected function realizeBatchAdjustment(Collection $sb2Group, IntegrationSetting $integrationSetting): void
    {
        $batchStockAdjustmentArray = $sb2Group
            ->map(function (SB2 $sb2) use ($integrationSetting): MercosStockAdjustment {
                $mercosProductId = Product::query()
                    ->where('erp_flex_id', $sb2->SB2_ID)
                    ->first()
                    ->mercos_id;

                $totalItemBalance = $integrationSetting->uses_only_default_warehouse_in_stock_calculations
                    ? (float) $sb2->sb2Sbw()->where('SB2_SBW_IDSBW', 0)->first()->SB2_SBW_QAtu
                    : (float) $sb2->SB2_QAtu;

                $balance = $integrationSetting->uses_reserved_quantity_in_stock_calculations
                    ? round(($totalItemBalance - (float) $sb2->SB2_Reserva), 4)
                    : round($totalItemBalance, 4);

                return new MercosStockAdjustment($mercosProductId, $balance);
            })
            ->toArray();

        $response = $this->mercosStockAdjustmentService->batchAdjustStock(
            new MercosBatchStockAdjustment($batchStockAdjustmentArray)
        );

        sleep($response->delayForRateLimiting);

        if (!$response->success) {
            $response = $this->mercosStockAdjustmentService->batchAdjustStock(
                new MercosBatchStockAdjustment($batchStockAdjustmentArray)
            );
        }
    }
}
