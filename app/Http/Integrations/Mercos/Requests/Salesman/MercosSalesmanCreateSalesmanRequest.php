<?php

namespace App\Http\Integrations\Mercos\Requests\Salesman;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosSalesman;
use App\Http\Integrations\Mercos\Requests\MercosBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosSalesmanCreateSalesmanRequest extends MercosBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosSalesmen $mercosSalesman
     */
    public function __construct(protected MercosSalesman $mercosSalesman)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/clientes';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return [
            'nome' => $this->mercosSalesman->nome,
            'email' => $this->mercosSalesman->email,
            'telefone' => $this->mercosSalesman->telefone,
            'administrador' => $this->mercosSalesman->administrador,
            'excluido' => $this->mercosSalesman->excluido,
        ];
    }
}
