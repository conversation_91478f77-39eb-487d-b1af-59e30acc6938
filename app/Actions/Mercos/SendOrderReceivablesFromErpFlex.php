<?php

namespace App\Actions\Mercos;

use App\Http\Integrations\ErpFlex\Services\ErpFlexReceivableService;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderReceivable;
use App\Http\Integrations\Mercos\Services\MercosReceivableService;
use App\Models\ErpFlex\SE2;
use App\Models\ErpFlex\SF2;
use App\Models\Order;
use App\Models\Receivable;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class SendOrderReceivablesFromErpFlex
{
    use AsAction;

    protected bool $force = false;

    protected MercosReceivableService $mercosReceivableService;
    protected ErpFlexReceivableService $erpFlexReceivableService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(tenant('id') . '_cadastros');
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_orders_automation) {
            return;
        }

        $this->force = $force;
        $this->mercosReceivableService = new MercosReceivableService();
        $this->erpFlexReceivableService = new ErpFlexReceivableService();

        $this->sendReceivables();
    }

    /**
     * Send pending receivables.
     *
     * @return void
     */
    protected function sendReceivables(): void
    {
        try {
            Order::query()
                ->whereNotNull('invoice_id')
                ->where('receivables_sent', false)
                ->lazyById(1000)
                ->each(function (Order $order): void {
                    $this->processSingleOrder($order);
                });
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Process a single order.
     *
     * @param Order $order
     * @return void
     */
    protected function processSingleOrder(Order $order): void
    {
        $sf2Items = SF2::query()
            ->with('sc5', 'sf3')
            ->whereHas('sc5', function (Builder $query) use ($order): Builder {
                return $query->where('SC5_ID', $order->erp_flex_id);
            })
            ->whereHas('sf3')
            ->get();

        foreach ($sf2Items as $sf2) {
            /** @var \App\Models\ErpFlex\SF2 $sf2 */
            $this->sendMercosInvoice($order, $sf2);
        }
    }

    /**
     * Send receivable to Mercos.
     *
     * @param Order $order
     * @param SF2 $sf2
     * @return void
     */
    protected function sendMercosInvoice(Order $order, SF2 $sf2): void
    {
        $receivableProcessed = false;

        $se2Items = SE2::query()
            ->where('SE2_IDSF2', $sf2->SF2_ID)
            ->get();

        foreach ($se2Items as $erpFlexReceivable) {
            /** @var \App\Models\ErpFlex\SE2 $erpFlexReceivable */

            $paidDate = null;
            $receivableProcessed = true;

            $receivable = Receivable::updateOrCreate([
                'order_id' => $order->id,
                'erp_flex_receivable_id' => $erpFlexReceivable->SE2_ID,
            ]);

            if (!empty($erpFlexReceivable->SE2_Baixa)
                && $erpFlexReceivable->SE2_Baixa != '0000-00-00 00:00:00') {
                $paidDate = (new Carbon($erpFlexReceivable->SE2_Baixa))->format('Y-m-d');
            }

            $mercosObject = new MercosOrderReceivable(
                cliente_id: $order->mercos_data['cliente_id'],
                numero_documento: $erpFlexReceivable->SE2_NumBoleto,
                valor: (float) $erpFlexReceivable->SE2_Valor,
                data_vencimento: (new Carbon($erpFlexReceivable->SE2_Vencto))->format('Y-m-d'),
                pedido_id: $order->mercos_id,
                arquivo_pdf: $this->getReceivableAttachment($erpFlexReceivable),
                data_pagamento: $paidDate,
                observacao: $erpFlexReceivable->SE2_Hist ?? null,
            );

            $response = $this->mercosReceivableService->createReceivable($mercosObject);

            sleep($response->delayForRateLimiting);

            if (!$response->success) {
                $response = $this->mercosReceivableService->createReceivable($mercosObject);
            }

            if ($response->mercosId) {
                $receivable->update([
                    'mercos_receivable_id' => $response->mercosId,
                ]);
            }
        }

        if (!$receivableProcessed) {
            return;
        }

        $order->update([
            'receivables_sent' => true,
        ]);
    }

    protected function getReceivableAttachment(SE2 $se2): string | null
    {
        return $this->erpFlexReceivableService->getById(
            $se2->SE2_ID,
            $se2->SE2_IDSA6,
            $se2->SE2_IDSA7
        );
    }
}
