<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosCustomerXSalesman
{
    /**
     * Create a new instance.
     *
     * @param  int|null $cliente_id
     * @param  int|null $usuario_id
     * @param  boolean|null $liberado
     */
    public function __construct(
        public ?int $cliente_id,
        public ?int $usuario_id,
        public ?bool $liberado
    ) {
    }

    /**
     * Create a new instance from an object.
     *
     * @param  object $object
     * @return static
     */
    public static function fromObject(object $object): static
    {
        return new static(
            cliente_id: $object->cliente_id,
            usuario_id: $object->usuario_id,
            liberado: $object->liberado,
        );
    }
}
