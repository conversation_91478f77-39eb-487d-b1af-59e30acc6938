<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosBatchStockAdjustment;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosBatchStockAdjustmentResponse;
use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\StockAdjustment\MercosStockAdjustmentAdjustBatchStockRequest;

class MercosStockAdjustmentService extends MercosBaseService
{
    use HandlesLogCreation;

    /**
     * Realize a batch adjustment for given products.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosBatchStockAdjustment $mercosBatchStockAdjustment
     * @return mixed
     */
    public function batchAdjustStock(MercosBatchStockAdjustment $mercosBatchStockAdjustment): mixed
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $request = new MercosStockAdjustmentAdjustBatchStockRequest($mercosBatchStockAdjustment);

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request($request)
            ->send();

        $this->createLog(
            $response,
            requestBody: $request->defaultData()
        );

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosBatchStockAdjustmentResponse(
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 200
        );
    }
}
