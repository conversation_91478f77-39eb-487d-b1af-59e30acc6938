<?php

namespace App\Http\Integrations\Mercos\Requests\PriceTableProduct;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTableProduct;
use App\Http\Integrations\Mercos\Requests\MercosBasePutRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosPriceTableProductDeletePriceTableProductRequest extends MercosBasePutRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTableProduct $mercosPriceTableProduct
     */
    public function __construct(protected int $id, protected MercosPriceTableProduct $mercosPriceTableProduct)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/produtos_tabela_preco/' . $this->id;
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        $this->mercosPriceTableProduct->excluido = true;
        return $this->mercosPriceTableProduct->toArray(true);
    }
}
