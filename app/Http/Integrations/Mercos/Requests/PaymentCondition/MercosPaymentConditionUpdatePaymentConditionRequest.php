<?php

namespace App\Http\Integrations\Mercos\Requests\PaymentCondition;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosPaymentCondition;
use App\Http\Integrations\Mercos\Requests\MercosBasePutRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosPaymentConditionUpdatePaymentConditionRequest extends MercosBasePutRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPaymentCondition $mercosPaymentCondition
     */
    public function __construct(
        protected int $id,
        protected MercosPaymentCondition $mercosPaymentCondition
    ) {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/condicoes_pagamento/' . $this->id;
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return [
            'nome' => $this->mercosPaymentCondition->nome,
            'valor_minimo' => $this->mercosPaymentCondition->valor_minimo,
            'excluido' => $this->mercosPaymentCondition->excluido,
        ];
    }
}
