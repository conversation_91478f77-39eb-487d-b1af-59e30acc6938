<?php

namespace App\Filament\Resources\PaymentConditionResource\Pages;

use App\Console\Commands\SyncPaymentConditions;
use App\Filament\Resources\PaymentConditionResource;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Support\Facades\Artisan;
use Throwable;

class ManagePaymentConditions extends ManageRecords
{
    protected static string $resource = PaymentConditionResource::class;

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            Action::make('Mapear formas de pagamento')
                ->visible(!integration_settings()->payment_conditions_pairing_finished)
                ->url(route('payment-conditions.pairing'))
                ->icon('heroicon-o-cog')
                ->color('secondary'),
            Action::make('Forçar sincronização')
                ->visible(integration_settings()->payment_conditions_pairing_finished)
                ->action(function (): void {
                    try {
                        Artisan::call(SyncPaymentConditions::class, ['force' => true]);
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->color('warning')
                ->requiresConfirmation(),
            Action::make('Sincronizar')
                ->visible(integration_settings()->payment_conditions_pairing_finished)
                ->action(function (): void {
                    try {
                        Artisan::call(SyncPaymentConditions::class);
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->requiresConfirmation(),
        ];
    }
}
