<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CategoryResource\Concerns\HandlesCategoryResourceForm;
use App\Filament\Resources\CategoryResource\Concerns\HandlesCategoryResourceTable;
use App\Filament\Resources\CategoryResource\Pages\ManageCategories;
use App\Models\Category;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

class CategoryResource extends Resource
{
    use HandlesCategoryResourceForm;
    use HandlesCategoryResourceTable;

    protected static ?string $model = Category::class;
    protected static ?string $modelLabel = 'categoria';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Configure the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => ManageCategories::route('/'),
        ];
    }
}
