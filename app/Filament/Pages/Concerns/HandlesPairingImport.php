<?php

namespace App\Filament\Pages\Concerns;

use Konnco\FilamentImport\Actions\ImportAction;
use Konnco\FilamentImport\Actions\ImportField;

trait HandlesPairingImport
{
    protected function defineImportAction(mixed $erpFlexCollection, mixed $mercosCollection, mixed $model)
    {
        return ImportAction::make()
            ->label('Importar planilha')
            ->handleBlankRows(true)
            ->color('success')
            ->icon('heroicon-s-document-report')
            ->fields([
                ImportField::make('erp_flex_id')
                    ->label('ErpFlex ID'),
                ImportField::make('mercos_id')
                    ->label('Mercos ID'),
            ])
            ->handleRecordCreation(function (array $data) use ($erpFlexCollection, $mercosCollection, $model) {
                $this->mapImportedItem(
                    $erpFlexCollection,
                    $mercosCollection,
                    $data['erp_flex_id'] ?? null,
                    $data['mercos_id'] ?? null,
                );

                // The model is not used. But it's returned in order to work with the plugin.
                return $model;
            });
    }

    /**
     * Imports pairing from a sheet.
     *
     * @param integer|string|null $erpFlexId
     * @param integer|string|null $mercosId
     * @return mixed
     */
    protected function mapImportedItem(mixed $erpFlexCollection, mixed $mercosCollection, int | string $erpFlexId = null, int | string $mercosId = null)
    {
        if (empty($erpFlexId) || empty($mercosId)) {
            return;
        }

        $erpFlexItem = $erpFlexCollection->where('erp_flex_id', $erpFlexId)->first();
        $mercosItem = $mercosCollection->where('mercos_id', $mercosId)->first();

        if (is_null($erpFlexItem) || is_null($mercosItem)) {
            return;
        }

        if ($this->mercosOriginFlow) {
            $erpFlexItem->mercos_id = $mercosItem->mercos_id;
            $erpFlexItem->mercos_data = $mercosItem->mercos_data;
            $erpFlexItem->save();
        } else {
            $mercosItem->erp_flex_id = $erpFlexItem->erp_flex_id;
            $mercosItem->erp_flex_data = $erpFlexItem->erp_flex_data;
            $mercosItem->save();
        }

        return redirect()->to(route($this->routeName));
    }
}
