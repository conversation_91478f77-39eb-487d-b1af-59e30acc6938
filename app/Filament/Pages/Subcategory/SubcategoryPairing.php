<?php

namespace App\Filament\Pages\Subcategory;

use App\Actions\Mercos\CreateCategoriesFromErpFlex;
use App\Actions\Mercos\CreateMercosCategoriesFromMercos;
use App\Actions\Pairing\SubcategoryPairing as SubcategoryPairingAction;
use App\Filament\Pages\Concerns\HandlesPairingImport;
use App\Filament\Resources\SubcategoryResource;
use App\Models\MercosCategory;
use App\Models\SubcategoryPairing as SubcategoryPairingModel;
use Awcodes\FilamentTableRepeater\Components\TableRepeater;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Collection;
use Throwable;

class SubcategoryPairing extends ListRecords
{
    use InteractsWithForms;
    use HandlesPairingImport;

    protected static ?string $title = 'Mapeamento de Subcategorias';
    protected static string $view = 'filament.pages.subcategories.subcategory-pairing';
    protected static string $resource = SubcategoryResource::class;

    protected string $routeName = 'subcategories.pairing';
    protected bool $mercosOriginFlow = false;

    /**
     * @var \Illuminate\Support\Collection
     */
    public $erpFlexSubcategoriesCollection;

    public array $erpFlexSubcategories;

    /**
     * @var \Illuminate\Support\Collection
     */
    public $mercosSubcategories;

    /**
     * @var \Illuminate\Support\Collection
     */
    public Collection $erpFlexOptions;

    /**
     * @var \Illuminate\Support\Collection
     */
    public  Collection $mercosOptions;

    /** @inheritDoc */
    public function mount(): void
    {
        $this->setSubcategoryData();
    }

    /** @inheritDoc */
    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return false;
    }

    /**
     * Sets the subcategories pairing data
     *
     * @return void
     */
    protected function setSubcategoryData()
    {
        $this->erpFlexSubcategoriesCollection = SubcategoryPairingModel::get();
        $this->erpFlexSubcategories = $this->erpFlexSubcategoriesCollection->toArray();
        $this->mercosSubcategories = MercosCategory::get();

        $this->erpFlexOptions = $this->erpFlexSubcategoriesCollection->mapWithKeys(function ($item) {
            return [
                $item->erp_flex_id => !empty($item->erp_flex_data['SBA_Desc'])
                    ? $item->erp_flex_data['SBA_Desc']
                    : 'Sem nome'
            ];
        });

        $this->mercosOptions = $this->mercosSubcategories->mapWithKeys(function ($item) {
            return [
                $item->mercos_id => !empty($item->mercos_data['nome'])
                    ? $item->mercos_data['nome']
                    : 'Sem nome'
            ];
        });
    }

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            Action::make('Aprovar mapeamento')
                ->action(function (): void {
                    try {
                        $this->onApproveMapping();
                        success_notification('O mapeamento foi realizado com sucesso')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-check')
                ->color('warning')
                ->requiresConfirmation(),
            $this->defineImportAction($this->erpFlexSubcategoriesCollection, $this->mercosSubcategories, new SubcategoryPairingModel()),
            Action::make('Salvar rascunho')
                ->action(function (): void {
                    try {
                        $this->onSaveMapping();
                        success_notification('O rascunho foi salvo com sucesso')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->color('success')
                ->icon('heroicon-s-save'),
            Action::make('Sincronizar dados')
                ->action(function (): void {
                    try {
                        $this->onRefreshData();
                        success_notification('Os dados foram atualizados')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->color('success')
                ->icon('heroicon-s-arrow-path')
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            Section::make('Mapear subcategorias')
                ->compact()
                ->schema([
                    Grid::make(1)->schema([
                        TableRepeater::make('erpFlexSubcategories')
                            ->disableItemCreation()
                            ->disableItemMovement()
                            ->disableItemDeletion()
                            ->disableLabel()
                            ->label('')
                            ->columns(2)
                            ->schema([
                                Select::make('erp_flex_id')
                                    ->disabled()
                                    ->disableLabel()
                                    ->label('Subcategoria ErpFlex (um ID por linha)')
                                    ->options($this->erpFlexOptions),
                                Select::make('mercos_id')
                                    ->disableLabel()
                                    ->label('Subcategoria Mercos (um ID por linha)')
                                    ->options($this->mercosOptions)
                                    ->searchable()
                            ])
                    ])
                ]),
        ];
    }

    /**
     * Saves the current mapping collection
     *
     * @return void
     */
    public function onSaveMapping(): void
    {
        foreach ($this->erpFlexSubcategories as $subcategory) {
            $mercosData = null;

            if (!empty($subcategory['mercos_id'])) {
                $mercosData = $this->mercosSubcategories
                    ->where('mercos_id', $subcategory['mercos_id'])
                    ->first()?->mercos_data;
            }

            SubcategoryPairingModel::updateOrCreate(
                ['id' => $subcategory['id']],
                [
                    'mercos_id' => $subcategory['mercos_id'] ?? null,
                    'mercos_data' => $mercosData
                ]
            );
        }
    }

    /**
     * Transforms the current mapping into the proper models.
     *
     * @return mixed
     */
    public function onApproveMapping(): mixed
    {
        $this->onSaveMapping();
        SubcategoryPairingAction::run();

        return redirect()->to('/app/subcategories');
    }

    /**
     * Refreshes the data on both sides.
     * The data is the same for the categories,
     * because Mercos doesn have the subacategory type.
     *
     * @return mixed
     */
    public function onRefreshData()
    {
        CreateCategoriesFromErpFlex::run(true);
        CreateMercosCategoriesFromMercos::run(true);

        return redirect()->to(route('subcategories.pairing'));
    }
}
