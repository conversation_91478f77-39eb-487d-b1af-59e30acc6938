<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosSalesman
{
    /**
     * Create a new instance.
     *
     * @param  string|null $nome
     * @param  int|null $id
     */
    public function __construct(
        public ?string $nome,
        public ?int $id = null,
        public ?string $email,
        public ?string $telefone,
        public ?bool $administrador,
        public ?bool $excluido,
        public ?string $ultima_alteracao,
    ) {
    }

    /**
     * Create a new instance from an object.
     *
     * @param  object $object
     * @return static
     */
    public static function fromObject(object $object): static
    {
        return new static(
            id: $object->id,
            nome: $object->nome,
            email: $object->email,
            telefone: $object->telefone,
            administrador: $object->administrador,
            excluido: $object->excluido,
            ultima_alteracao: $object->ultima_alteracao
        );
    }
}
