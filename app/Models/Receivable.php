<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Receivable model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $order_id
 * @property  string $erp_flex_receivable_id
 * @property  string $mercos_receivable_id
 * @property  array $erp_flex_receivable_data
 * @property  array $mercos_receivable_data
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class Receivable extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'order_id',
        'erp_flex_receivable_id',
        'mercos_receivable_id',
        'erp_flex_receivable_data',
        'mercos_receivable_data',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'erp_flex_receivable_data' => 'array',
        'mercos_receivable_data' => 'array',
    ];
}
