<?php

namespace App\Http\Integrations\Mercos\Requests\Customer;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosCustomer;
use App\Http\Integrations\Mercos\Requests\MercosBasePutRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosCustomerUpdateCustomerRequest extends MercosBasePutRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosCustomer $mercosCustomer
     */
    public function __construct(
        protected int $id,
        protected MercosCustomer $mercosCustomer
    ) {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/clientes/' . $this->id;
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return [
            'razao_social' => $this->mercosCustomer->razao_social,
            'nome_fantasia' => $this->mercosCustomer->nome_fantasia,
            'tipo' => $this->mercosCustomer->tipo,
            'cnpj' => $this->mercosCustomer->cnpj,
            'inscricao_estadual' => $this->mercosCustomer->inscricao_estadual,
            'suframa' => $this->mercosCustomer->suframa,
            'rua' => $this->mercosCustomer->rua,
            'numero' => $this->mercosCustomer->numero,
            'complemento' => $this->mercosCustomer->complemento,
            'cep' => $this->mercosCustomer->cep,
            'bairro' => $this->mercosCustomer->bairro,
            'cidade' => $this->mercosCustomer->cidade,
            'estado' => $this->mercosCustomer->estado,
            'observacao' => $this->mercosCustomer->observacao,
            'emails' => $this->mercosCustomer->emails,
            'telefones' => $this->mercosCustomer->telefones,
            'contatos' => $this->mercosCustomer->contatos,
            'criador_id' => $this->mercosCustomer->criador_id,
            'segmento_id' => $this->mercosCustomer->segmento_id,
            'rede_id' => $this->mercosCustomer->rede_id,
            'bloqueado_b2b' => $this->mercosCustomer->bloqueado_b2b,
            'excluido' => $this->mercosCustomer->excluido,
            'enderecos_adicionais' => $this->mercosCustomer->enderecos_adicionais,
            'ultima_alteracao' => $this->mercosCustomer->ultima_alteracao,
        ];
    }
}
