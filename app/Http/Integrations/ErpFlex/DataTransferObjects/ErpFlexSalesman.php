<?php

namespace App\Http\Integrations\ErpFlex\DataTransferObjects;

class ErpFlexSalesman
{
    /**
     * Create a new instance.
     *
     * @param  string $descricao
     * @param  string $email
     * @param  string|null $endereco
     * @param  string|null $ddd_tel
     * @param  string|null $telefone
     * @param  string|null $ddd_cel
     * @param  string|null $tel_celular
     * @param  string|null $ddd_fax
     * @param  string|null $fax
     * @param  string|null $bairro
     * @param  string|null $municipio
     * @param  string|null $estado
     * @param  string|null $cep
     * @param  string|null $cpf
     * @param  string|null $comissao
     * @param  string|null $desconto_maximo
     * @param  string|null $numero
     * @param  string|null $complemento
     * @param  string|null $ativo
     * @param  string|null $id
     */
    public function __construct(
        public ?string $descricao,
        public ?string $email,
        public ?string $endereco = null,
        public ?string $ddd_tel = null,
        public ?string $telefone = null,
        public ?string $ddd_cel = null,
        public ?string $tel_celular = null,
        public ?string $ddd_fax = null,
        public ?string $fax = null,
        public ?string $bairro = null,
        public ?string $municipio = null,
        public ?string $estado = null,
        public ?string $cep = null,
        public ?string $cpf = null,
        public ?string $comissao = null,
        public ?string $desconto_maximo = null,
        public ?string $numero = null,
        public ?string $complemento = null,
        public ?string $ativo = null,
        public ?string $id = null
    ) {
    }

    /**
     * @inheritDoc
     */
    public function toArray(bool $create = false): array
    {
        $data = [
            'descricao' => $this->descricao,
            'email' => $this->email,
            'endereco' => $this->endereco,
            'ddd_tel' => $this->ddd_tel,
            'telefone' => $this->telefone,
            'ddd_cel' => $this->ddd_cel,
            'tel_celular' => $this->tel_celular,
            'ddd_fax' => $this->ddd_fax,
            'fax' => $this->fax,
            'bairro' => $this->bairro,
            'municipio' => $this->municipio,
            'estado' => $this->estado,
            'cep' => $this->cep,
            'cpf' => $this->cpf,
            'comissao' => $this->comissao,
            'desconto_maximo' => $this->desconto_maximo,
            'numero' => $this->numero,
            'complemento' => $this->complemento,
            'ativo' => $this->ativo,
            'id' => $this->id,
        ];

        if ($create) {
            unset($data['id']);
        }

        return $data;
    }
}
