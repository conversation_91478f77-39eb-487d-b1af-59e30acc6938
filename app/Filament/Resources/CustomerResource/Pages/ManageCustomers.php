<?php

namespace App\Filament\Resources\CustomerResource\Pages;

use App\Console\Commands\SyncCustomers;
use App\Filament\Resources\CustomerResource;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Support\Facades\Artisan;
use Throwable;

class ManageCustomers extends ManageRecords
{
    protected static string $resource = CustomerResource::class;

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            Action::make('map clients')
                ->label('Mapear clientes')
                ->visible(!integration_settings()->customers_pairing_finished && !integration_settings()->enables_customer_mercos_to_erp_flex_flow)
                ->url(route('customers.pairing'))
                ->icon('heroicon-o-cog')
                ->color('secondary'),
            Action::make('map clients mercos flow')
                ->label('Mapear clientes')
                ->visible(!integration_settings()->customers_pairing_finished && integration_settings()->enables_customer_mercos_to_erp_flex_flow)
                ->url(route('customers.pairing-mercos'))
                ->icon('heroicon-o-cog')
                ->color('secondary'),
            Action::make('Forçar sincronização')
                ->visible(integration_settings()->customers_pairing_finished)
                ->action(function (): void {
                    try {
                        Artisan::call(SyncCustomers::class, ['force' => true]);
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->color('warning')
                ->requiresConfirmation(),
            Action::make('Sincronizar')
                ->visible(integration_settings()->customers_pairing_finished)
                ->action(function (): void {
                    try {
                        Artisan::call(SyncCustomers::class);
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->requiresConfirmation(),
        ];
    }
}
