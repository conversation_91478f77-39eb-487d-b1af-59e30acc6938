<?php

namespace App\Filament\Pages\Haulier;

use App\Actions\Mercos\CreateHauliersFromErpFlex;
use App\Actions\Mercos\CreateMercosHauliersFromMercos;
use App\Actions\Pairing\HaulierPairing as HaulierPairingAction;
use App\Filament\Pages\Concerns\HandlesPairingImport;
use App\Filament\Resources\HaulierResource;
use App\Models\MercosHaulier;
use App\Models\HaulierPairing as HaulierPairingModel;
use Awcodes\FilamentTableRepeater\Components\TableRepeater;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Collection;
use Throwable;

class HaulierPairing extends ListRecords
{
    use InteractsWithForms;
    use HandlesPairingImport;

    protected static ?string $title = 'Mapeamento de Transportadoras';
    protected static string $view = 'filament.pages.hauliers.haulier-pairing';
    protected static string $resource = HaulierResource::class;

    protected string $routeName = 'hauliers.pairing';
    protected bool $mercosOriginFlow = false;

    /**
     * @var \Illuminate\Support\Collection
     */
    public $erpFlexHauliersCollection;

    public array $erpFlexHauliers;

    /**
     * @var \Illuminate\Support\Collection
     */
    public $mercosHauliers;

    /**
     * @var \Illuminate\Support\Collection
     */
    public Collection $erpFlexOptions;

    /**
     * @var \Illuminate\Support\Collection
     */
    public  Collection $mercosOptions;

    /** @inheritDoc */
    public function mount(): void
    {
        $this->setHaulierData();
    }

    /** @inheritDoc */
    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return false;
    }

    /**
     * Sets the hauliers pairing data
     *
     * @return void
     */
    protected function setHaulierData()
    {
        $this->erpFlexHauliersCollection = HaulierPairingModel::get();
        $this->erpFlexHauliers = $this->erpFlexHauliersCollection->toArray();
        $this->mercosHauliers = MercosHaulier::get();

        $this->erpFlexOptions = $this->erpFlexHauliersCollection->mapWithKeys(function ($item) {
            $name = trim(
                ($item->erp_flex_data['SA4_Desc'] ?? '') . ' ' . ($item->erp_flex_data['SA4_Fantasia'] ?? '')
            );

            return [
                $item->erp_flex_id => $name,
            ];
        });

        $this->mercosOptions = $this->mercosHauliers->mapWithKeys(function ($item) {
            return [
                $item->mercos_id => !empty($item->mercos_data['nome'])
                    ? $item->mercos_data['nome']
                    : 'Sem nome'
            ];
        });
    }

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            Action::make('Aprovar mapeamento')
                ->action(function (): void {
                    try {
                        $this->onApproveMapping();
                        success_notification('O mapeamento foi realizado com sucesso')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-check')
                ->color('warning')
                ->requiresConfirmation(),
            $this->defineImportAction($this->erpFlexHauliersCollection, $this->mercosHauliers, new HaulierPairingModel()),
            Action::make('Salvar rascunho')
                ->action(function (): void {
                    try {
                        $this->onSaveMapping();
                        success_notification('O rascunho foi salvo com sucesso')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->color('success')
                ->icon('heroicon-s-save'),
            Action::make('Sincronizar dados')
                ->action(function (): void {
                    try {
                        $this->onRefreshData();
                        success_notification('Os dados foram atualizados')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->color('success')
                ->icon('heroicon-s-arrow-path')
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            Section::make('Mapear transportadoras')
                ->compact()
                ->schema([
                    Grid::make(1)->schema([
                        TableRepeater::make('erpFlexHauliers')
                            ->disableItemCreation()
                            ->disableItemMovement()
                            ->disableItemDeletion()
                            ->disableLabel()
                            ->label('')
                            ->columns(2)
                            ->schema([
                                Select::make('erp_flex_id')
                                    ->disabled()
                                    ->disableLabel()
                                    ->label('Transportadora ErpFlex (um ID por linha)')
                                    ->options($this->erpFlexOptions),
                                Select::make('mercos_id')
                                    ->disableLabel()
                                    ->label('Transportadora Mercos (um ID por linha)')
                                    ->options($this->mercosOptions)
                                    ->searchable()
                            ])
                    ])
                ]),
        ];
    }

    /**
     * Saves the current mapping collection
     *
     * @return void
     */
    public function onSaveMapping(): void
    {
        foreach ($this->erpFlexHauliers as $haulier) {
            $mercosData = null;

            if (!empty($haulier['mercos_id'])) {
                $mercosData = $this->mercosHauliers
                ->where('mercos_id', $haulier['mercos_id'])
                ->first()?->mercos_data;
            }

            HaulierPairingModel::updateOrCreate(
                ['id' => $haulier['id']],
                [
                    'mercos_id' => $haulier['mercos_id'] ?? null,
                    'mercos_data' => $mercosData
                ]
            );
        }
    }

    /**
     * Transforms the current mapping into the proper models.
     *
     * @return mixed
     */
    public function onApproveMapping(): mixed
    {
        $this->onSaveMapping();
        HaulierPairingAction::run();

        return redirect()->to('/app/hauliers');
    }

    /**
     * Refreshes the data on both sides.
     *
     * @return mixed
     */
    public function onRefreshData()
    {
        CreateHauliersFromErpFlex::run(true);
        CreateMercosHauliersFromMercos::run(true);

        return redirect()->to(route('hauliers.pairing'));
    }
}
