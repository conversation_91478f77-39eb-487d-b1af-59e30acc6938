<?php

namespace App\Console;

use App\Actions\RunErpFlexBillingFlow;
use App\Actions\RunErpFlexManagementFlow;
use App\Actions\RunErpFlexPriceFlow;
use App\Actions\RunErpFlexStockFlow;
use App\Actions\RunMercosManagementFlow;
use App\Actions\RunMercosOrdersFlow;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    protected $commands = [
        RunErpFlexManagementFlow::class,
        RunErpFlexStockFlow::class,
        RunErpFlexPriceFlow::class,
        RunErpFlexBillingFlow::class,
        RunMercosManagementFlow::class,
        RunMercosOrdersFlow::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // ERPFlex > Mercos
        $schedule->command(RunErpFlexManagementFlow::class)->everyThirtyMinutes();
        $schedule->command(RunErpFlexStockFlow::class)->cron('5,15,25,35,45,55 * * * *');
        $schedule->command(RunErpFlexPriceFlow::class)->hourly();
        $schedule->command(RunErpFlexBillingFlow::class)->hourly();

        // Mercos > ERPFlex
        $schedule->command(RunMercosManagementFlow::class)->everyThirtyMinutes();
        $schedule->command(RunMercosOrdersFlow::class)->cron('0,10,20,30,40,50 * * * *');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
