<?php

namespace App\Actions\ErpFlex;

use App\Actions\Mercos\Concerns\HandlesMercosDefaultApiCollectionGetRequests;
use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexSalesman;
use App\Http\Integrations\ErpFlex\Services\ErpFlexSalesmanService;
use App\Http\Integrations\Mercos\Services\MercosSalesmanService;
use App\Models\Salesman;
use App\Models\ErpFlex\SA3;
use App\Models\IntegrationSetting;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateSalesmenFromMercos
{
    use AsAction;
    use HandlesMercosDefaultApiCollectionGetRequests;

    protected bool $force = false;

    protected MercosSalesmanService $mercosSalesmanService;
    protected ErpFlexSalesmanService $erpFlexSalesmanService;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue('erp_flex_management_' . tenant('id'));
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_salesmen_automation) {
            return;
        }

        $this->force = $force;
        $this->mercosSalesmanService = new MercosSalesmanService();
        $this->erpFlexSalesmanService = new ErpFlexSalesmanService();

        $this->integrateSalesmen();
    }

    /**
     * Integrate all salesmen from ERPFlex to Mercos.
     *
     * @return void
     */
    protected function integrateSalesmen(): void
    {
        try {
            $integrationSetting = IntegrationSetting::first();
            $executionStart = now();

            $mercosSalesmen = $this->getSalesmenCollection();

            foreach ($mercosSalesmen as $mercosSalesman) {
                try {
                    $this->processSingleSalesman($mercosSalesman);
                } catch (Throwable $th) {
                    error($th);
                }

                sleep(1);
            }

            $integrationSetting->update(['last_salesman_update' => $executionStart]);
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Get the customers collection for a given offset page.
     *
     * @return \Illuminate\Support\Collection
     */
    protected function getSalesmenCollection(): Collection
    {
        $lastUpdatedAt = Carbon::parse(integration_settings()->last_salesman_update)
            ->setTimezone('America/Sao_Paulo')->format('Y-m-d H:i:s');

        return $this->getMercosApiCollection($this->mercosSalesmanService, $lastUpdatedAt);
    }

    /**
     * Process a single salesman.
     *
     * @param  mixed $mercosApiSalesman
     * @return void
     */
    protected function processSingleSalesman($mercosApiSalesman): void
    {
        /** @var \App\Models\Salesman $salesman */
        $salesman = Salesman::updateOrCreate([
            'mercos_id' => $mercosApiSalesman->id
        ], [
            'mercos_data' => json_decode(json_encode($mercosApiSalesman), true)
        ]);

        $erpFlexApiSaleman = SA3::query()
            ->select(['SA3_ID'])
            ->whereRaw('trim(SA3_EMail) = ?', trim($mercosApiSalesman->email))
            ->where('SA3_EMail', '<>', '')
            ->first();

        if (!is_null($erpFlexApiSaleman)) {
            $salesman->erp_flex_id = $erpFlexApiSaleman->SA3_ID;
            $salesman->save();
            // @todo - Quando encontrar, precisa atualizar o erpflex com as informações do mercos.
            // Sobscrever?

            return;
        }

        if (!$salesman->erp_flex_id) {
            $this->createSingleSalesman($salesman, $mercosApiSalesman);
            return;
        }

        $this->updateSingleSalesman($salesman, $mercosApiSalesman);
    }

    /**
     * Create a single salesman.
     *
     * @param  \App\Models\Salesman $salesman
     * @param  mixed $mercosApiSalesman
     * @return void
     */
    protected function createSingleSalesman(Salesman $salesman, mixed $mercosApiSalesman): void
    {
        $phone = $this->buildPhonesData($mercosApiSalesman);

        $data = new ErpFlexSalesman(
            descricao: $mercosApiSalesman->nome,
            email: $mercosApiSalesman->email,
            ddd_tel: isset($phone['ddd']) ? $phone['ddd'] : null,
            telefone: isset($phone['number']) ? $phone['number'] : null,
            ativo: true
        );

        $erpFlexId = $this->erpFlexSalesmanService->create($data);

        $salesman->erp_flex_id = $erpFlexId;
        $salesman->save();
    }

    /**
     * Update a single salesman.
     *
     * @param  \App\Models\Salesman $salesman
     * @param  mixed $mercosApiSalesman
     * @return void
     */
    protected function updateSingleSalesman(Salesman $salesman, mixed $mercosApiSalesman): void
    {
        $phone = $this->buildPhonesData($mercosApiSalesman);

        $data = new ErpFlexSalesman(
            descricao: $mercosApiSalesman->nome,
            email: $mercosApiSalesman->email,
            ddd_tel: isset($phone['ddd']) ? $phone['ddd'] : null,
            telefone: isset($phone['number']) ? $phone['number'] : null,
            ativo: true
        );

        $this->erpFlexSalesmanService->update($salesman->erp_flex_id, $data);
    }

    /**
     * Builds the phone data.
     *
     * @param mixed $mercosApiSalesman
     * @return array
     */
    protected function buildPhonesData(mixed $mercosApiSalesman): array
    {
        if (empty($mercosApiSalesman->telefone)) {
            return [];
        }

        $phone = preg_replace('/[^0-9]/', '', $mercosApiSalesman->telefone);
        $ddd = substr($phone, 0, 2);

        return [
            'ddd' => $ddd,
            'number' => substr($phone, 2)
        ];
    }
}
