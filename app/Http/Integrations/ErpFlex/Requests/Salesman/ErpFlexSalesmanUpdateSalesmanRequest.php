<?php

namespace App\Http\Integrations\ErpFlex\Requests\Salesman;

use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexSalesman;
use App\Http\Integrations\ErpFlex\Requests\ErpFlexBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class ErpFlexSalesmanUpdateSalesmanRequest extends ErpFlexBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexSalesman $erpFlexSalesman
     */
    public function __construct(
        protected int $id,
        protected ErpFlexSalesman $erpFlexSalesman
    ) {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/api/vendedor/' . $this->id;
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return $this->erpFlexSalesman->toArray(false);
    }
}
