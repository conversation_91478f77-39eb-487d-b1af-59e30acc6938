<?php

namespace App\Filament\Resources\HaulierResource\Concerns;

use App\Models\Haulier;
use Filament\Tables\Columns\TextColumn;

trait HandlesHaulierResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')->label('ID'),
            TextColumn::make('erp_flex_id')->label('ID ERPFlex'),
            TextColumn::make('mercos_id')->label('ID Mercos'),
            TextColumn::make('name')
                ->label('Nome')
                ->formatStateUsing(fn (Haulier $record): string => $record->erp_flex_data['SA4_Desc']),
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [];
    }
}
