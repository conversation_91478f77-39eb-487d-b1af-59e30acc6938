<?php

namespace App\Filament\Resources\IntegrationSettingResource\Concerns;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Toggle;

trait HandlesIntegrationSettingResourceForm
{
    /**
     * Configure the resource's form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Section::make('Habilitação de fluxos')
                ->compact()
                ->schema([
                    Grid::make(2)->schema([
                        Toggle::make('enables_customer_automation')->label('Cliente'),
                        Toggle::make('enables_customer_mercos_to_erp_flex_flow')->label('Fluxo: Mercos ->> ErpFlex')
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('enables_products_automation')->label('Produto/categoria'),
                        Toggle::make('enables_price_table_automation')->label('Tabela de preços'),
                        Toggle::make('enables_stock_automation')->label('Estoque'),
                        Toggle::make('enables_hauliers_automation')->label('Transportadora')
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('enables_orders_automation')->label('Pedidos/orçamentos'),
                        Toggle::make('enables_payment_conditions_automation')->label('Formas de pagamento'),
                    ]),
                    Grid::make(1)->schema([
                        Toggle::make('enables_salesmen_automation')->label('Vendedores')
                    ]),
                ])
        ];
    }
}
