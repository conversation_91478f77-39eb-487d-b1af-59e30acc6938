<?php

namespace App\Actions\Mercos;

use App\Actions\Mercos\Concerns\HandlesMercosDefaultApiCollectionGetRequests;
use App\Http\Integrations\Mercos\Services\MercosProductService;
use App\Models\IntegrationSetting;
use App\Models\MercosProduct;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateMercosProductsFromMercos
{
    use AsAction;
    use HandlesMercosDefaultApiCollectionGetRequests;

    protected bool $force = false;

    protected MercosProductService $mercosProductService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(tenant('id') . '_cadastros');
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_products_automation) {
            return;
        }

        $this->force = $force;

        $this->mercosProductService = new MercosProductService();

        $this->integrateProducts();
    }

    /**
     * Integrate all products Mercos for pairing.
     *
     * @return void
     */
    protected function integrateProducts(): void
    {
        try {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();
            $executionStart = now();

            $mercosApiProducts = $this->getProductsCollection();

            foreach ($mercosApiProducts as $mercosApiProduct) {
                $this->processSingleProduct($mercosApiProduct);
            }

            $integrationSetting->update(['last_products_update' => $executionStart]);
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Get the products collection for a given offset page.
     *
     * @return \Illuminate\Support\Collection
     */
    protected function getProductsCollection(): Collection
    {
        $lastUpdatedAt = Carbon::parse(integration_settings()->last_products_update)
            ->setTimezone('America/Sao_Paulo')->format('Y-m-d H:i:s');

        return $this->getMercosApiCollection($this->mercosProductService, $lastUpdatedAt);
    }

    /**
     * Process a single product.
     *
     * @param  mixed $mercosApiProduct
     * @return void
     */
    protected function processSingleProduct(mixed $mercosApiProduct): void
    {
        MercosProduct::updateOrCreate([
            'mercos_id' => $mercosApiProduct->id
        ], [
            'mercos_data' => json_decode(json_encode($mercosApiProduct), true),
        ]);
    }
}
