<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosCustomer
{
    /**
     * Create a new instance.
     *
     * @param  string|null $razao_social
     * @param  string|null $nome_fantasia
     * @param  string|null $cnpj
     * @param  int|null $id
     * @param  string|null $tipo
     * @param  string|null $inscricao_estadual
     * @param  string|null $suframa
     * @param  string|null $rua
     * @param  string|null $numero
     * @param  string|null $complemento
     * @param  string|null $cep
     * @param  string|null $bairro
     * @param  string|null $cidade
     * @param  string|null $estado
     * @param  string|null $observacao
     * @param  array|null $emails
     * @param  array|null $telefones
     * @param  array|null $contatos
     * @param  int|null $criador_id
     * @param  int|null $segmento_id
     * @param  int|null $rede_id
     * @param  bool|null $bloqueado_b2b
     * @param  bool|null $excluido
     * @param  array|null $enderecos_adicionais
     * @param  string|null $ultima_alteracao
     */
    public function __construct(
        public ?string $razao_social,
        public ?string $nome_fantasia,
        public ?string $cnpj,
        public ?int $id = null,
        public ?string $tipo = null,
        public ?string $inscricao_estadual = null,
        public ?string $suframa = null,
        public ?string $rua = null,
        public ?string $numero = null,
        public ?string $complemento = null,
        public ?string $cep = null,
        public ?string $bairro = null,
        public ?string $cidade = null,
        public ?string $estado = null,
        public ?string $observacao = null,
        public ?array $emails = [],
        public ?array $telefones = [],
        public ?array $contatos = [],
        public ?int $criador_id = null,
        public ?int $segmento_id = null,
        public ?int $rede_id = null,
        public ?bool $bloqueado_b2b = null,
        public ?bool $excluido = null,
        public ?array $enderecos_adicionais = [],
        public ?string $ultima_alteracao = null
    ) {
    }

    /**
     * Create a new instance from an object.
     *
     * @param  object $object
     * @return static
     */
    public static function fromObject(object $object): static
    {
        return new static(
            id: $object->id,
            razao_social: $object->razao_social,
            nome_fantasia: $object->nome_fantasia,
            tipo: $object->tipo,
            cnpj: $object->cnpj,
            inscricao_estadual: $object->inscricao_estadual,
            suframa: $object->suframa,
            rua: $object->rua,
            numero: $object->numero,
            complemento: $object->complemento,
            cep: $object->cep,
            bairro: $object->bairro,
            cidade: $object->cidade,
            estado: $object->estado,
            observacao: $object->observacao,
            emails: $object->emails,
            telefones: $object->telefones,
            contatos: $object->contatos,
            criador_id: $object->criador_id,
            segmento_id: $object->segmento_id,
            rede_id: $object->rede_id,
            bloqueado_b2b: $object->bloqueado_b2b,
            excluido: $object->excluido,
            enderecos_adicionais: $object->enderecos_adicionais,
            ultima_alteracao: $object->ultima_alteracao,
        );
    }
}
