<?php

namespace App\Actions\Mercos;

use App\Actions\Mercos\Concerns\HandlesMercosDefaultApiCollectionGetRequests;
use App\Http\Integrations\Mercos\Services\MercosProductCategoryService;
use App\Models\IntegrationSetting;
use App\Models\MercosCategory;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateMercosCategoriesFromMercos
{
    use AsAction;
    use HandlesMercosDefaultApiCollectionGetRequests;

    protected bool $force = false;

    protected MercosProductCategoryService $mercosCategoryService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(tenant('id') . '_cadastros');
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_products_automation) {
            return;
        }

        $this->force = $force;

        $this->mercosCategoryService = new MercosProductCategoryService();

        $this->integrateCategories();
    }

    /**
     * Integrate all categories Mercos for pairing.
     *
     * @return void
     */
    protected function integrateCategories(): void
    {
        try {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();
            $executionStart = now();

            $mercosApiCategories = $this->getCategoriesCollection();

            foreach ($mercosApiCategories as $mercosApiCategory) {
                $this->processSingleCategory($mercosApiCategory);
            }

            $integrationSetting->update(['last_categories_update' => $executionStart]);
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Get the categories collection for a given offset page.
     *
     * @return \Illuminate\Support\Collection
     */
    protected function getCategoriesCollection(): Collection
    {
        $lastUpdatedAt = Carbon::parse(integration_settings()->last_categories_update)
            ->setTimezone('America/Sao_Paulo')->format('Y-m-d H:i:s');

        return $this->getMercosApiCollection($this->mercosCategoryService, $lastUpdatedAt);
    }

    /**
     * Process a single categoty.
     *
     * @param  mixed $mercosApiCategory
     * @return void
     */
    protected function processSingleCategory(mixed $mercosApiCategory): void
    {
        MercosCategory::updateOrCreate([
            'mercos_id' => $mercosApiCategory->id
        ], [
            'mercos_data' => json_decode(json_encode($mercosApiCategory), true),
        ]);
    }
}
