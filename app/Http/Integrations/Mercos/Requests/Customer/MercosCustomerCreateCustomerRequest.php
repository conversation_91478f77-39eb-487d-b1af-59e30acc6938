<?php

namespace App\Http\Integrations\Mercos\Requests\Customer;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosCustomer;
use App\Http\Integrations\Mercos\Requests\MercosBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosCustomerCreateCustomerRequest extends MercosBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosCustomer $mercosCustomer
     */
    public function __construct(protected MercosCustomer $mercosCustomer)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/clientes';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return [
            'razao_social' => $this->mercosCustomer->razao_social,
            'nome_fantasia' => $this->mercosCustomer->nome_fantasia,
            'tipo' => $this->mercosCustomer->tipo,
            'cnpj' => $this->mercosCustomer->cnpj,
            'inscricao_estadual' => $this->mercosCustomer->inscricao_estadual,
            'suframa' => $this->mercosCustomer->suframa,
            'rua' => $this->mercosCustomer->rua,
            'numero' => $this->mercosCustomer->numero,
            'complemento' => $this->mercosCustomer->complemento,
            'cep' => $this->mercosCustomer->cep,
            'bairro' => $this->mercosCustomer->bairro,
            'cidade' => $this->mercosCustomer->cidade,
            'estado' => $this->mercosCustomer->estado,
            'observacao' => $this->mercosCustomer->observacao,
            'emails' => $this->mercosCustomer->emails,
            'telefones' => $this->mercosCustomer->telefones,
            'contatos' => $this->mercosCustomer->contatos
        ];
    }
}
