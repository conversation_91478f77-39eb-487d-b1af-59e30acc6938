<?php

namespace App\Http\Integrations\Mercos\Requests\PriceTableProduct;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTableProduct;
use App\Http\Integrations\Mercos\Requests\MercosBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosPriceTableProductCreatePriceTableProductRequest extends MercosBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTableProduct $mercosPriceTableProduct
     */
    public function __construct(protected MercosPriceTableProduct $mercosPriceTableProduct)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/produtos_tabela_preco';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return array_filter(
            $this->mercosPriceTableProduct->toArray(true)
        );
    }
}
