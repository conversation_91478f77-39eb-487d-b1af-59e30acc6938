<?php

namespace App\Actions;

use App\Actions\Mercos\CreateOrderBillingsFromErpFlex;
use App\Actions\Mercos\CreatePricesTableFromErpFlex;
use App\Actions\Mercos\CreatePriceTableProductFromErpFlex;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class RunErpFlexBillingFlow
{
    use AsAction;

    public string $commandSignature = 'efm:run-erp-flex-billing-flow {tenant?} {force?}';
    public string $commandDescription = 'Runs the ERPFlex > Mercos billing flow.';

    /**
     * Handle the action as a command.
     *
     * @param  \Illuminate\Console\Command $command
     * @return void
     */
    public function asCommand(Command $command): void
    {
        $this->handle(
            tenant: $command->argument('tenant'),
            force: (bool) $command->argument('force')
        );
    }

    public function handle(?string $tenant = null, bool $force = false)
    {
        $tenants = Tenant::query()
            ->when($tenant, fn (Builder $query): Builder => $query->where('id', $tenant))
            ->get();

        tenancy()->runForMultiple($tenants, function () use ($force) {
            CreateOrderBillingsFromErpFlex::dispatch($force);
        });
    }
}
