<?php

namespace App\Models\ErpFlex;

use App\Models\ErpFlex\Scopes\HasCompanyScope;
use Illuminate\Database\Eloquent\Model;

/**
 * Customer table for the ErpFlex.
 */
class BaseModel extends Model
{
    protected $connection = 'erpflex_mysql';
    protected $prefix = '';

    /**
     * Create a new instance.
     */
    public function __construct()
    {
        parent::__construct();

        $this->connection = get_erp_flex_connection_mode();
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::addGlobalScope(new HasCompanyScope);
    }

    public function getPrefix()
    {
        return $this->prefix;
    }
}
