<?php

namespace App\Filament\Admin\Resources\TenantResource\Pages;

use App\Filament\Admin\Resources\TenantResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewTenant extends ViewRecord
{
    protected static string $resource = TenantResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data = array_merge($data, [
            'erp_flex_api_username' => base_decrypt($this->record->erp_flex['api']['username']),
            'erp_flex_api_password' => base_decrypt($this->record->erp_flex['api']['password']),
            'erp_flex_tenant_id' => $this->record->erp_flex['company_id'],
            'erp_flex_multi_database' => $this->record->erp_flex['multi_database'],
            'erp_flex_connection_mode' => $this->record->erp_flex['connection_mode'],
            'mercos_api_company_token' => base_decrypt($this->record->mercos['api']['company_token']),
            'mercos_connection_mode' => $this->record->mercos['connection_mode'],
        ]);

        return $data;
    }
}
