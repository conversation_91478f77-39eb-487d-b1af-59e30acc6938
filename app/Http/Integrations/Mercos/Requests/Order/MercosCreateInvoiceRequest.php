<?php

namespace App\Http\Integrations\Mercos\Requests\Order;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderInvoice;
use App\Http\Integrations\Mercos\Requests\MercosBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosCreateInvoiceRequest extends MercosBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderInvoice $mercosOrderInvoice
     */
    public function __construct(protected MercosOrderInvoice $mercosOrderInvoice)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/nota_fiscal';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return [
            'cliente_id' => $this->mercosOrderInvoice->cliente_id,
            'pedido_id' => $this->mercosOrderInvoice->pedido_id,
            'numero' => $this->mercosOrderInvoice->numero,
            'serie' => $this->mercosOrderInvoice->serie,
            'chave_acesso' => $this->mercosOrderInvoice->chave_acesso,
            'valor' => $this->mercosOrderInvoice->valor,
            'data_emissao' => $this->mercosOrderInvoice->data_emissao,
            'arquivo_xml' => $this->mercosOrderInvoice->arquivo_xml,
            // 'link_xml' => $this->mercosOrderInvoice->link_xml,
            // 'arquivo_pdf' => $this->mercosOrderInvoice->arquivo_pdf,
            // 'link_pdf' => $this->mercosOrderInvoice->link_pdf,
            'excluido' => is_null($this->mercosOrderInvoice->excluido)
                ? false
                : $this->mercosOrderInvoice->excluido,
        ];
    }
}
