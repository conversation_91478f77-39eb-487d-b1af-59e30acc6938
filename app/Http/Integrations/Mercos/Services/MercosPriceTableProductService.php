<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosBatchUpdatePriceTableProduct;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTableProduct;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosBatchUpdatePriceTableProductsResponse;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosPriceProductTableResponse;
use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\PriceTableProduct\MercosPriceTableProductCreatePriceTableProductRequest;
use App\Http\Integrations\Mercos\Requests\PriceTableProduct\MercosPriceTableProductDeletePriceTableProductRequest;
use App\Http\Integrations\Mercos\Requests\PriceTableProduct\MercosPriceTableProductGetPriceTableProductRequest;
use App\Http\Integrations\Mercos\Requests\PriceTableProduct\MercosPriceTableProductGetPriceTableProductsRequest;
use App\Http\Integrations\Mercos\Requests\PriceTableProduct\MercosPriceTableProductUpdateBatchPriceTableProductRequest;
use App\Http\Integrations\Mercos\Requests\PriceTableProduct\MercosPriceTableProductUpdatePriceTableProductRequest;

class MercosPriceTableProductService extends MercosBaseService
{
    use HandlesLogCreation;

    /**
     * Get all price table products.
     *
     * @return mixed
     */
    public function get(): mixed
    {
        return json_decode(
            (new MercosConnector($this->applicationToken, $this->companyToken))
                ->request(new MercosPriceTableProductGetPriceTableProductsRequest())
                ->send()
        );
    }

    /**
     * Get a price table product by its ID.
     *
     * @param  int $id
     * @return mixed
     */
    public function getById(int $id): mixed
    {
        return json_decode(
            (new MercosConnector($this->applicationToken, $this->companyToken))
                ->request(new MercosPriceTableProductGetPriceTableProductRequest($id))
                ->send()
        );
    }

    /**
     * Create a price table product.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTableProduct $mercosPriceTableProduct
     * @return mixed
     */
    public function create(MercosPriceTableProduct $mercosPriceTableProduct): mixed
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosPriceTableProductCreatePriceTableProductRequest($mercosPriceTableProduct))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosPriceProductTableResponse(
            mercosId: !empty($response->header('MeusPedidosID'))
                ? $response->header('MeusPedidosID')
                : null,
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 201
        );
    }

    /**
     * Handle the price table products batch processing.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosBatchUpdatePriceTableProduct $mercosBatchUpdatePriceTableProduct
     * @return mixed
     */
    public function handleBatch(MercosBatchUpdatePriceTableProduct $mercosBatchUpdatePriceTableProduct): mixed
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $request = new MercosPriceTableProductUpdateBatchPriceTableProductRequest($mercosBatchUpdatePriceTableProduct);

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request($request)
            ->send();

        $success = $response->status() === 201;

        /** @var \App\Models\Log $log */
        $log = $this->createLog(
            response: $response,
            success: $success,
            requestBody: $request->defaultData(),
        );

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosBatchUpdatePriceTableProductsResponse(
            data: $log->response_body,
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 200
        );
    }

    /**
     * Update a price table product.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTableProduct $mercosPriceTableProduct
     * @return mixed
     */
    public function update(int $id, MercosPriceTableProduct $mercosPriceTableProduct): mixed
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosPriceTableProductUpdatePriceTableProductRequest($id, $mercosPriceTableProduct))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosPriceProductTableResponse(
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 200
        );
    }

    /**
     * Delete a price table product.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTableProduct $mercosPriceTableProduct
     * @return mixed
     */
    public function delete(int $id, MercosPriceTableProduct $mercosPriceTableProduct): mixed
    {
        return (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosPriceTableProductDeletePriceTableProductRequest($id, $mercosPriceTableProduct))
            ->send()
            ->ok();
    }
}
