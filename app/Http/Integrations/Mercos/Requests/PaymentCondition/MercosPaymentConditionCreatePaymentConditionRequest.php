<?php

namespace App\Http\Integrations\Mercos\Requests\PaymentCondition;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosPaymentCondition;
use App\Http\Integrations\Mercos\Requests\MercosBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosPaymentConditionCreatePaymentConditionRequest extends MercosBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPaymentCondition $mercosPaymentCondition
     */
    public function __construct(protected MercosPaymentCondition $mercosPaymentCondition)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/condicoes_pagamento';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return [
            'nome' => $this->mercosPaymentCondition->nome,
            'valor_minimo' => $this->mercosPaymentCondition->valor_minimo,
            'excluido' => $this->mercosPaymentCondition->excluido,
        ];
    }
}
