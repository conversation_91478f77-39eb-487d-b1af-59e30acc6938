<?php

namespace App\Filament\Pages\Product;

use App\Actions\Mercos\CreateMercosProductsFromMercos;
use App\Actions\Mercos\CreateProductsFromErpFlex;
use App\Actions\Pairing\ProductPairing as ProductPairingAction;
use App\Filament\Pages\Concerns\HandlesPairingImport;
use App\Filament\Resources\ProductResource;
use App\Models\MercosProduct;
use App\Models\ProductPairing as ProductPairingModel;
use Awcodes\FilamentTableRepeater\Components\TableRepeater;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Collection;
use Throwable;

class ProductPairing extends ListRecords
{
    use InteractsWithForms;
    use HandlesPairingImport;

    protected static ?string $title = 'Mapeamento de Produtos';
    protected static string $view = 'filament.pages.products.product-pairing';
    protected static string $resource = ProductResource::class;

    protected string $routeName = 'products.pairing';
    protected bool $mercosOriginFlow = false;

    /**
     * @var \Illuminate\Support\Collection
     */
    public $erpFlexProductsCollection;

    public array $erpFlexProducts;

    /**
     * @var \Illuminate\Support\Collection
     */
    public $mercosProducts;

    /**
     * @var \Illuminate\Support\Collection
     */
    public Collection $erpFlexOptions;

    /**
     * @var \Illuminate\Support\Collection
     */
    public  Collection $mercosOptions;

    /** @inheritDoc */
    public function mount(): void
    {
        $this->setProductData();
    }

    /** @inheritDoc */
    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return false;
    }

    /**
     * Sets the products pairing data
     *
     * @return void
     */
    protected function setProductData()
    {
        $this->erpFlexProductsCollection = ProductPairingModel::get();
        $this->erpFlexProducts = $this->erpFlexProductsCollection->toArray();
        $this->mercosProducts = MercosProduct::get();

        $this->erpFlexOptions = $this->erpFlexProductsCollection->mapWithKeys(function ($item) {
            return [
                $item->erp_flex_id => !empty($item->erp_flex_data['sb1']['SB1_Desc'])
                    ? $item->erp_flex_data['sb1']['SB1_Desc']
                    : 'Sem nome'
            ];
        });

        $this->mercosOptions = $this->mercosProducts->mapWithKeys(function ($item) {
            return [
                $item->mercos_id => !empty($item->mercos_data['nome'])
                    ? $item->mercos_data['nome']
                    : 'Sem nome'
            ];
        });
    }

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            Action::make('Aprovar mapeamento')
                ->action(function (): void {
                    try {
                        $this->onApproveMapping();
                        success_notification('O mapeamento foi realizado com sucesso')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-check')
                ->color('warning')
                ->requiresConfirmation(),
            $this->defineImportAction($this->erpFlexProductsCollection, $this->mercosProducts, new ProductPairingModel()),
            Action::make('Salvar rascunho')
                ->action(function (): void {
                    try {
                        $this->onSaveMapping();
                        success_notification('O rascunho foi salvo com sucesso')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->color('success')
                ->icon('heroicon-s-save'),
            Action::make('Sincronizar dados')
                ->action(function (): void {
                    try {
                        $this->onRefreshData();
                        success_notification('Os dados foram atualizados')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->color('success')
                ->icon('heroicon-s-arrow-path')
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            Section::make('Mapear produtos')
                ->compact()
                ->schema([
                    Grid::make(1)->schema([
                        TableRepeater::make('erpFlexProducts')
                            ->disableItemCreation()
                            ->disableItemMovement()
                            ->disableItemDeletion()
                            ->disableLabel()
                            ->label('')
                            ->columns(2)
                            ->schema([
                                Select::make('erp_flex_id')
                                    ->disabled()
                                    ->disableLabel()
                                    ->label('Produto ErpFlex (um ID por linha)')
                                    ->options($this->erpFlexOptions),
                                Select::make('mercos_id')
                                    ->disableLabel()
                                    ->label('Produto Mercos (um ID por linha)')
                                    ->options($this->mercosOptions)
                                    ->searchable()
                            ])
                    ])
                ]),
        ];
    }

    /**
     * Saves the current mapping collection
     *
     * @return void
     */
    public function onSaveMapping(): void
    {
        foreach ($this->erpFlexProducts as $product) {
            $mercosData = null;

            if (!empty($product['mercos_id'])) {
                $mercosData = $this->mercosProducts
                ->where('mercos_id', $product['mercos_id'])
                ->first()?->mercos_data;
            }

            ProductPairingModel::updateOrCreate(
                ['id' => $product['id']],
                [
                    'mercos_id' => $product['mercos_id'] ?? null,
                    'mercos_data' => $mercosData
                ]
            );
        }
    }

    /**
     * Transforms the current mapping into the proper models.
     *
     * @return mixed
     */
    public function onApproveMapping(): mixed
    {
        $this->onSaveMapping();
        ProductPairingAction::run();

        return redirect()->to('/app/products');
    }

    /**
     * Refreshes the data on both sides.
     *
     * @return mixed
     */
    public function onRefreshData()
    {
        CreateProductsFromErpFlex::run(true);
        CreateMercosProductsFromMercos::run(true);

        return redirect()->to(route('products.pairing'));
    }
}
