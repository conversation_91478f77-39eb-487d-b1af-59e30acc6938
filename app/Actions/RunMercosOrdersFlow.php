<?php

namespace App\Actions;

use App\Actions\ErpFlex\CreateOrdersFromMercos;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class RunMercosOrdersFlow
{
    use AsAction;

    public string $commandSignature = 'efm:run-mercos-orders-flow {tenant?} {force?}';
    public string $commandDescription = 'Runs the Mercos > ERPFlex orders\' flow.';

    /**
     * Handle the action as a command.
     *
     * @param  \Illuminate\Console\Command $command
     * @return void
     */
    public function asCommand(Command $command): void
    {
        $this->handle(
            tenant: $command->argument('tenant'),
            force: (bool) $command->argument('force')
        );
    }

    /**
     * Handle the action.
     *
     * @param  string|null $tenant
     * @param  bool $force
     * @return void
     */
    public function handle(?string $tenant = null, bool $force = false): void
    {
        $tenants = Tenant::query()
            ->when($tenant, fn (Builder $query): Builder => $query->where('id', $tenant))
            ->get();

        tenancy()->runForMultiple($tenants, function () use ($force) {
            CreateOrdersFromMercos::dispatch($force, null, true);
        });
    }
}
