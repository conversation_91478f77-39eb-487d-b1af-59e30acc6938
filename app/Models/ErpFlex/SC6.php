<?php

namespace App\Models\ErpFlex;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SC6 extends BaseModel
{
    protected $table = 'SC6';
    protected $primaryKey = 'SC6_ID';
    protected $prefix = 'SC6';

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'SC6_DT_INC' => 'datetime',
        'SC6_DT_ALT' => 'datetime',
    ];

    public function sc5(): BelongsTo
    {
        return $this->belongsTo(SC5::class, 'SC6_IDSC5', 'SC5_ID');
    }
}
