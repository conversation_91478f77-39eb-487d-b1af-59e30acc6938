<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderInvoice;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosCreateInvoiceResponse;
use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\Order\MercosCreateInvoiceRequest;

class MercosInvoiceService extends MercosBaseService
{
    use HandlesLogCreation;

    /**
     * Create a create a invoice from an order.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderInvoice $mercosInvoice
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosCreateInvoiceResponse
     */
    public function createInvoice(MercosOrderInvoice $mercosInvoice): MercosCreateInvoiceResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosCreateInvoiceRequest($mercosInvoice))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosCreateInvoiceResponse(
            mercosId: !empty($response->header('MeusPedidosID'))
                ? $response->header('MeusPedidosID')
                : null,
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 201
        );
    }
}
