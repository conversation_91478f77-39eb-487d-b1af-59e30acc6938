<?php

namespace App\Actions\Mercos;

use App\Actions\Mercos\Concerns\HandlesMercosDefaultApiCollectionGetRequests;
use App\Http\Integrations\Mercos\Services\MercosHaulierService;
use App\Models\IntegrationSetting;
use App\Models\MercosHaulier;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateMercosHauliersFromMercos
{
    use AsAction;
    use HandlesMercosDefaultApiCollectionGetRequests;

    protected bool $force = false;

    protected MercosHaulierService $mercosHaulierService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(tenant('id') . '_cadastros');
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_hauliers_automation) {
            return;
        }

        $this->force = $force;

        $this->mercosHaulierService = new MercosHaulierService();

        $this->integrateHauliers();
    }

    /**
     * Integrate all hauliers Mercos for pairing.
     *
     * @return void
     */
    protected function integrateHauliers(): void
    {
        try {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();
            $executionStart = now();

            $mercosApiHauliers = $this->getHauliersCollection();

            foreach ($mercosApiHauliers as $mercosApiHaulier) {
                $this->processSingleHaulier($mercosApiHaulier);
            }

            $integrationSetting->update(['last_hauliers_update' => $executionStart]);
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Get the hauliers collection for a given offset page.
     *
     * @return \Illuminate\Support\Collection
     */
    protected function getHauliersCollection(): Collection
    {
        $lastUpdatedAt = Carbon::parse(integration_settings()->last_hauliers_update)
            ->setTimezone('America/Sao_Paulo')->format('Y-m-d H:i:s');

        return $this->getMercosApiCollection($this->mercosHaulierService, $lastUpdatedAt);
    }

    /**
     * Process a single haulier.
     *
     * @param  mixed $mercosApiHaulier
     * @return void
     */
    protected function processSingleHaulier(mixed $mercosApiHaulier): void
    {
        /** @var \App\Models\Haulier $haulier */
        $haulier = MercosHaulier::updateOrCreate([
            'mercos_id' => $mercosApiHaulier->id
        ], [
            'mercos_data' => json_decode(json_encode($mercosApiHaulier), true),
        ]);
    }
}
