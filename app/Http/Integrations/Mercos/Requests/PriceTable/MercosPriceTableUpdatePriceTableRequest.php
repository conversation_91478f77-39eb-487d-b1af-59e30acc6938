<?php

namespace App\Http\Integrations\Mercos\Requests\PriceTable;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTable;
use App\Http\Integrations\Mercos\Requests\MercosBasePutRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosPriceTableUpdatePriceTableRequest extends MercosBasePutRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTable $mercosPriceTable
     */
    public function __construct(protected int $id, protected MercosPriceTable $mercosPriceTable)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/tabelas_preco/' . $this->id;
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return array_filter($this->mercosPriceTable->toArray());
    }
}
