<?php

namespace App\Filament\Resources\ProductResource\Concerns;

use App\Actions\Mercos\CreateProductsFromErpFlex;
use App\Models\ErpFlex\SB2;
use App\Models\Product;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables;
use Filament\Tables\Filters\Filter;
use Throwable;

trait HandlesProductResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')->label('ID'),
            TextColumn::make('erp_flex_id')->label('ID ERPFlex'),
            TextColumn::make('mercos_id')->label('ID Mercos'),
            TextColumn::make('created_at')
                ->label('Nome')
                ->formatStateUsing(fn (Product $product): string => $product->erp_flex_data['sb1']['SB1_Desc'] ?? ''),
            TextColumn::make('updated_at')
                ->label('Código')
                ->formatStateUsing(fn (Product $product): string => $product->erp_flex_data['SB2_Codigo'] ?? ''),
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [
            Tables\Actions\ActionGroup::make([
                Tables\Actions\Action::make('reprocess')
                    ->label('Reprocessar')
                    ->visible(fn (Product $product): bool => is_null($product->mercos_id))
                    ->requiresConfirmation()
                    ->action(function (Product $product) {
                        try {
                            CreateProductsFromErpFlex::run(true, $product->erp_flex_id, null, null);
                            success_notification('O produto foi integrado.')->send();
                            return redirect()->route('filament.app.resources.products.index');
                        } catch (Throwable) {
                            error_notification('Não foi possível integrar o produto. Tente novamente mais tarde.')->send();
                        }
                    }),
                Tables\Actions\Action::make('unlink')
                    ->label('Desvincular do Mercos')
                    ->visible(fn (Product $product): bool => !is_null($product->mercos_id))
                    ->requiresConfirmation()
                    ->action(function (Product $product) {
                        try {
                            $product->update(['mercos_id' => null]);
                            success_notification('O ID do produto do Mercos foi desvinculado desta plataforma. Agora, você já pode reprocessá-lo pelo mesmo menu. Não se esqueça de excluir o produto no Mercos também.')->send();
                            return redirect()->route('filament.app.resources.products.index');
                        } catch (Throwable) {
                            error_notification('Não foi possível desvincular o ID do produto. Tente novamente mais tarde.')->send();
                        }
                    })
            ]),
        ];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [];
    }
}
