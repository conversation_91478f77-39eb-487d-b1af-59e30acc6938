<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *  Log model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $service_name
 * @property  string $service_method
 * @property  bool $success
 * @property  string $status_code
 * @property  array $response_headers
 * @property  array $response_body
 * @property  string $method
 * @property  string $url
 * @property  array $request_headers
 * @property  array $request_body
 * @property  string $error_description
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class Log extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'service_name',
        'service_method',
        'success',
        'status_code',
        'response_headers',
        'response_body',
        'method',
        'url',
        'request_headers',
        'request_body',
        'error_description',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'response_headers' => 'array',
        'response_body' => 'array',
        'request_headers' => 'array',
        'request_body' => 'array'
    ];
}
