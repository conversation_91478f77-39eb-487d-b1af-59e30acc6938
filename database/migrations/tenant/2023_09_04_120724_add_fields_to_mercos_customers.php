<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('mercos_customers', function (Blueprint $table) {
            $table->string('erp_flex_id')->nullable();
            $table->json('erp_flex_data')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mercos_customers', function (Blueprint $table) {
            $table->dropColumn('erp_flex_id');
            $table->dropColumn('erp_flex_data');
        });
    }
};
