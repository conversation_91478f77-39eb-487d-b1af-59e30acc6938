<?php

namespace App\Actions;

use App\Actions\Mercos\UpdateProductsStockFromErpFlex;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class RunErpFlexStockFlow
{
    use AsAction;

    public string $commandSignature = 'efm:run-erp-flex-stock-flow {tenant?} {force?}';
    public string $commandDescription = 'Runs the ERPFlex > Mercos stock adjustment flow.';

    /**
     * Handle the action as a command.
     *
     * @param  \Illuminate\Console\Command $command
     * @return void
     */
    public function asCommand(Command $command): void
    {
        $this->handle($command->argument('tenant'));
    }

    /**
     * Handle the action.
     *
     * @param  string|null $tenant
     * @param  bool $force
     * @return void
     */
    public function handle(?string $tenant = null): void
    {
        $tenants = Tenant::query()
            ->when($tenant, fn (Builder $query): Builder => $query->where('id', $tenant))
            ->get();

        tenancy()->runForMultiple($tenants, function () {
            UpdateProductsStockFromErpFlex::dispatch(true);
        });
    }
}
