<?php

namespace App\Actions\ErpFlex;

use App\Actions\Mercos\Concerns\HandlesMercosDefaultApiCollectionGetRequests;
use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexCustomer;
use App\Http\Integrations\ErpFlex\Services\ErpFlexCustomerService;
use App\Http\Integrations\Mercos\Services\MercosCustomerService;
use App\Models\Customer;
use App\Models\CustomerPairing;
use App\Models\IntegrationSetting;
use App\Models\Tenant;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateCustomersFromMercos
{
    use AsAction;
    use HandlesMercosDefaultApiCollectionGetRequests;

    public string $commandSignature = 'efm:run-mercos-customer-management {tenant?} {force?}';
    public string $commandDescription = 'Runs the Mercos > ERPFlex customer\'s flow.';

    protected bool $force = false;
    protected bool $pairing = false;

    protected MercosCustomerService $mercosCustomerService;
    protected ErpFlexCustomerService $erpFlexCustomerService;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue('mercos_clientes_' . tenant('id'));
    }

    public function asCommand(Command $command): void
    {
        $tenants = Tenant::query()
            ->when($command->argument('tenant'), fn(Builder $query): Builder => $query->where('id', $command->argument('tenant')))
            ->get();

        tenancy()->runForMultiple($tenants, fn() => $this->handle((bool) $command->argument('force')));
    }

    public function handle(bool $force = false, ?int $mercosId = null, bool $reprocess = false): void
    {
        if (!integration_settings()->enables_customer_automation) {
            return;
        }

        $this->pairing = false;

        if (!integration_settings()->customers_pairing_finished) {
            $this->pairing = true;
        }

        $this->force = $force;
        $this->mercosCustomerService = new MercosCustomerService();
        $this->erpFlexCustomerService = new ErpFlexCustomerService();

        $this->integrateCustomers($mercosId, $reprocess);
    }

    protected function integrateCustomers(?int $mercosId = null, bool $reprocess = false): void
    {
        if ($reprocess) {
            $this->handleCustomersReprocessing($mercosId);
            return;
        }

        try {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();

            $executionStart = now();

            $mercosApiCustomers = $this->getCustomersCollection();
        } catch (Throwable $th) {
            throw_error($th);
        }

        foreach ($mercosApiCustomers as $mercosApiCustomer) {
            if (!is_null($mercosId) && $mercosApiCustomer->id !== $mercosId) {
                continue;
            }

            try {
                $this->processSingleCustomer($mercosApiCustomer);
            } catch (Throwable $th) {
                error($th);
            }
        }

        $integrationSetting->update(['last_mercos_customers_update' => $executionStart]);
    }

    protected function handleCustomersReprocessing(?int $mercosId = null): void
    {
        Customer::query()
            ->whereNull('erp_flex_id')
            ->when(!is_null($mercosId), fn(Builder $query): Builder => $query->where('mercos_id', $mercosId))
            ->get()
            ->each(function (Customer $customer): void {
                $this->createSingleCustomer($customer, json_decode(json_encode($customer->mercos_data)));
                sleep(1);
            });
    }

    protected function getCustomersCollection(): Collection
    {
        $lastUpdatedAt = Carbon::parse(integration_settings()->last_mercos_customers_update)
            ->setTimezone('America/Sao_Paulo')->format('Y-m-d H:i:s');

        return $this->getMercosApiCollection($this->mercosCustomerService, $lastUpdatedAt);
    }

    protected function processSingleCustomer(mixed $mercosApiCustomer): void
    {
        if ($mercosApiCustomer->excluido) {
            return;
        }

        if ($this->pairing) {
            CustomerPairing::updateOrCreate([
                'mercos_id' => $mercosApiCustomer->id
            ], [
                'mercos_data' => json_decode(json_encode($mercosApiCustomer), true),
            ]);

            return;
        }

        /** @var \App\Models\Customer $customer */
        $customer = Customer::updateOrCreate([
            'mercos_id' => $mercosApiCustomer->id
        ], [
            'mercos_data' => json_decode(json_encode($mercosApiCustomer), true),
        ]);

        if ($customer->erp_flex_id) {
            return;
        }

        $this->createSingleCustomer($customer, $mercosApiCustomer);
    }

    protected function createSingleCustomer(Customer $customer, mixed $mercosApiCustomer): void
    {
        $erpFlexCustomerObject = new ErpFlexCustomer(
            nome: $mercosApiCustomer->razao_social ?? null,
            fantasia: $mercosApiCustomer->nome_fantasia ?? null,
            cpf_cnpj: $mercosApiCustomer->cnpj ?? null,
            inscricao_estadual: $mercosApiCustomer->inscricao_estadual ?? null,
            endereco: $mercosApiCustomer->rua ?? null,
            numero: $mercosApiCustomer->numero ?? null,
            complemento: $mercosApiCustomer->complemento ?? null,
            cep: $mercosApiCustomer->cep ?? null,
            bairro: $mercosApiCustomer->bairro ?? null,
            municipio: $mercosApiCustomer->cidade ?? null,
            estado: $mercosApiCustomer->estado ?? null
        );

        $this->mapPhones($mercosApiCustomer, $erpFlexCustomerObject);

        $response = $this->erpFlexCustomerService->create($erpFlexCustomerObject);

        if ($response && $response->erpFlexId) {
            $customer->update(['erp_flex_id' => $response->erpFlexId]);
        }
    }

    protected function updateSingleCustomer(Customer $customer, mixed $mercosApiCustomer): void
    {
        $erpFlexCustomerObject = new ErpFlexCustomer(
            nome: $mercosApiCustomer->razao_social ?? null,
            fantasia: $mercosApiCustomer->nome_fantasia ?? null,
            cpf_cnpj: $mercosApiCustomer->cnpj ?? null,
            inscricao_estadual: $mercosApiCustomer->inscricao_estadual ?? null,
            endereco: $mercosApiCustomer->rua ?? null,
            numero: $mercosApiCustomer->numero ?? null,
            complemento: $mercosApiCustomer->complemento ?? null,
            cep: $mercosApiCustomer->cep ?? null,
            bairro: $mercosApiCustomer->bairro ?? null,
            municipio: $mercosApiCustomer->cidade ?? null,
            estado: $mercosApiCustomer->estado ?? null
        );

        $this->mapPhones($mercosApiCustomer, $erpFlexCustomerObject);

        $response = $this->erpFlexCustomerService->update($customer->erp_flex_id, $erpFlexCustomerObject);

        $customer->update([
            'erp_flex_id' => $response->erpFlexId
        ]);
    }

    protected function mapPhones(mixed $mercosApiCustomer, ErpFlexCustomer $erpFlexCustomerObject): void
    {
        if (empty($mercosApiCustomer->telefones)) {
            return;
        }

        foreach ($mercosApiCustomer->telefones as $mercosPhone) {
            $phone = $this->parsePhoneNumber($mercosPhone->numero);

            if ($phone['type'] === 'phone') {
                $erpFlexCustomerObject->ddd_residencial = $phone['code_area'];
                $erpFlexCustomerObject->telefone_residencial = $phone['number'];
            }

            if ($phone['type'] === 'cellphone') {
                $erpFlexCustomerObject->ddd_celular = $phone['code_area'];
                $erpFlexCustomerObject->telefone_celular = $phone['number'];
            }
        }
    }

    function parsePhoneNumber(?string $phoneNumber = null): array
    {
        if (is_null($phoneNumber)) {
            return ['code_area' => null, 'number' => null, 'type' => 'invalid'];
        }

        $phoneNumber = preg_replace("/[^0-9]/", "", $phoneNumber);

        if (substr($phoneNumber, 0, 2) === "55") {
            $phoneNumber = substr($phoneNumber, 2);
        }

        $type = (strlen($phoneNumber) == 10) ? 'phone' : ((strlen($phoneNumber) == 11) ? 'cellphone' : '');

        if ($type === '') {
            return ['code_area' => null, 'number' => null, 'type' => 'invalid'];
        }

        $codeArea = substr($phoneNumber, 0, 2);
        $number = substr($phoneNumber, 2);

        return [
            'code_area' => $codeArea,
            'number' => $number,
            'type' => $type,
        ];
    }
}
