<?php

namespace App\Models\ErpFlex;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class SD2 extends BaseModel
{
    protected $table = 'SD2';
    protected $primaryKey = 'SD2_ID';
    protected $prefix = 'SD2';

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'SD2_DT_INC' => 'datetime',
        'SD2_DT_ALT' => 'datetime',
    ];

    public function sf2(): BelongsTo
    {
        return $this->belongsTo(SF2::class, 'SD2_IDSF2', 'SF2_ID');
    }
}
