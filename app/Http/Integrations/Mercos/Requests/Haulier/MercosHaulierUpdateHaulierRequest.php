<?php

namespace App\Http\Integrations\Mercos\Requests\Haulier;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosHaulier;
use App\Http\Integrations\Mercos\Requests\MercosBasePutRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosHaulierUpdateHaulierRequest extends MercosBasePutRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosHaulier $mercosHaulier
     */
    public function __construct(
        protected int $id,
        protected MercosHaulier $mercosHaulier
    ) {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/transportadoras/' . $this->id;
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return [
            'nome' => $this->mercosHaulier->nome,
            'cidade' => $this->mercosHaulier->cidade,
            'estado' => $this->mercosHaulier->estado,
            'informacoes_adicionais' => $this->mercosHaulier->informacoes_adicionais,
            'telefones' => $this->mercosHaulier->telefones,
            'excluido' => is_null($this->mercosHaulier->excluido)
                ? false
                : $this->mercosHaulier->excluido,
        ];
    }
}
