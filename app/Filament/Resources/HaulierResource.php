<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HaulierResource\Concerns\HandlesHaulierResourceForm;
use App\Filament\Resources\HaulierResource\Concerns\HandlesHaulierResourceTable;
use App\Filament\Resources\HaulierResource\Pages;
use App\Filament\Resources\HaulierResource\RelationManagers;
use App\Models\Haulier;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class HaulierResource extends Resource
{
    use HandlesHaulierResourceForm;
    use HandlesHaulierResourceTable;

    protected static ?string $model = Haulier::class;
    protected static ?string $modelLabel = 'transportadora';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageHauliers::route('/'),
        ];
    }
}
