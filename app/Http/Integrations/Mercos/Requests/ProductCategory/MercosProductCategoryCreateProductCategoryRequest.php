<?php

namespace App\Http\Integrations\Mercos\Requests\ProductCategory;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosProductCategory;
use App\Http\Integrations\Mercos\Requests\MercosBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosProductCategoryCreateProductCategoryRequest extends MercosBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosProductCategory $mercosProductCategory
     */
    public function __construct(protected MercosProductCategory $mercosProductCategory)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/categorias';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return $this->mercosProductCategory->toArray(true);
    }
}
