<?php

namespace App\Actions\Mercos;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosHaulier;
use App\Http\Integrations\Mercos\Services\MercosHaulierService;
use App\Models\ErpFlex\SA4;
use App\Models\Haulier;
use App\Models\HaulierPairing;
use App\Models\IntegrationSetting;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateHauliersFromErpFlex
{
    use AsAction;

    protected bool $force = false;
    protected bool $pairing = false;

    protected MercosHaulierService $mercosHaulierService;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue('erp_flex_management_' . tenant('id'));
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_hauliers_automation) {
            return;
        }

        $this->pairing = false;

        if (!integration_settings()->hauliers_pairing_finished) {
            $this->pairing = true;
        }

        $this->force = $force;
        $this->mercosHaulierService = new MercosHaulierService();

        $this->integrateHauliers();
    }

    /**
     * Integrate all haulier conditions from ERPFlex to Mercos.
     *
     * @return void
     */
    protected function integrateHauliers(): void
    {
        try {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();
            $executionStart = now();

            $updateStartedAt = Carbon::parse($integrationSetting->last_haulier_update)
                ->setTimezone('America/Sao_Paulo');

            SA4::query()
                ->when(!$this->force, function (Builder $query) use ($updateStartedAt) : Builder {
                    return $query->where(function (Builder $query) use ($updateStartedAt) : Builder {
                        return $query
                            ->where('SA4_DT_INC', '>=', $updateStartedAt)
                            ->orWhere('SA4_DT_ALT', '>=', $updateStartedAt);
                    });
                })
                ->lazyById(1000)
                ->each(function (mixed $erpFlexApiHaulier): void {
                    $this->processSingleHaulier($erpFlexApiHaulier);
                });

            $integrationSetting->update(['last_haulier_update' => $executionStart]);
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Process a single haulier condition.
     *
     * @param  \App\Models\ErpFlex\SA4 $erpFlexApiHaulier
     * @return void
     */
    protected function processSingleHaulier(SA4 $erpFlexApiHaulier): void
    {
        if ($this->pairing) {
            HaulierPairing::updateOrCreate(
                ['erp_flex_id' => $erpFlexApiHaulier->SA4_ID],
                [
                    'erp_flex_data' => json_decode(json_encode($erpFlexApiHaulier), true),
                ],
            );

            return;
        }

        /** @var \App\Models\Haulier $haulier */
        $haulier = Haulier::updateOrCreate(
            ['erp_flex_id' => $erpFlexApiHaulier->SA4_ID],
            [
                'erp_flex_data' => json_decode(json_encode($erpFlexApiHaulier), true),
            ],
        );

        if (!$haulier->mercos_id) {
            $this->createSingleHaulier($haulier, $erpFlexApiHaulier);
            return;
        }

        $this->updateSingleHaulier($haulier, $erpFlexApiHaulier);
    }

    /**
     * Create a single haulier condition.
     *
     * @param  \App\Models\Haulier $haulier
     * @param  \App\Models\ErpFlex\SA4 $erpFlexApiHaulier
     * @return void
     */
    protected function createSingleHaulier(Haulier $haulier, SA4 $erpFlexApiHaulier): void
    {
        $mercosHaulierObject = new MercosHaulier(
            nome: textLimit($erpFlexApiHaulier->SA4_Desc),
            cidade: textLimit($erpFlexApiHaulier->SA4_Mun, 50),
            estado: textLimit($erpFlexApiHaulier->SA4_Est, 2),
            informacoes_adicionais: textLimit($erpFlexApiHaulier->SA4_Campo1, 500),
            telefones: $this->buildPhonesData($erpFlexApiHaulier)
        );

        $response = $this->mercosHaulierService->create($mercosHaulierObject);

        sleep($response->delayForRateLimiting);

        if (is_null($response->mercosId)) {
            $response = $this->mercosHaulierService->create($mercosHaulierObject);
        }

        $haulier->update([
            'mercos_id' => $response->mercosId
        ]);
    }

    /**
     * Update a single haulier condition.
     *
     * @param  \App\Models\Haulier $haulier
     * @param  mixed $erpFlexApiHaulier
     * @return void
     */
    protected function updateSingleHaulier(Haulier $haulier, SA4 $erpFlexApiHaulier): void
    {
        $mercosHaulierObject = new MercosHaulier(
            id: $haulier->mercos_id,
            nome: textLimit($erpFlexApiHaulier->SA4_Desc),
            cidade: textLimit($erpFlexApiHaulier->SA4_Mun, 50),
            estado: textLimit($erpFlexApiHaulier->SA4_Est, 2),
            informacoes_adicionais: textLimit($erpFlexApiHaulier->SA4_Campo1, 500),
            telefones: $this->buildPhonesData($erpFlexApiHaulier)
        );

        $response = $this->mercosHaulierService->update($haulier->mercos_id, $mercosHaulierObject);

        sleep($response->delayForRateLimiting);

        if (!$response->success) {
            $this->mercosHaulierService->update($haulier->mercos_id, $mercosHaulierObject);
        }
    }

    /**
     * Build the phones data.
     *
     * @param  \App\Models\ErpFlex\SA4 $erpFlexApiHaulier
     * @return array
     */
    protected function buildPhonesData(SA4 $erpFlexApiHaulier): array
    {
        $phones = [];

        if (!empty($erpFlexApiHaulier->SA4_Tel)) {
            $phones[] = [
                'numero' => trim($erpFlexApiHaulier->SA4_DDD) . trim($erpFlexApiHaulier->SA4_Tel)
            ];
        }

        return $phones;
    }
}
