<?php

namespace App\Actions\Mercos;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosCustomer;
use App\Http\Integrations\Mercos\Services\MercosCustomerService;
use App\Models\Customer;
use App\Models\ErpFlex\SA1;
use App\Models\IntegrationSetting;
use App\Models\CustomerPairing;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateCustomersFromErpFlex
{
    use AsAction;

    protected bool $force = false;
    protected bool $pairing = false;

    protected MercosCustomerService $mercosCustomerService;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue('erp_flex_management_' . tenant('id'));
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @param  int|null $erpFlexCustomerId
     * @param  bool $reprocess
     * @return void
     */
    public function handle(bool $force = false, ?int $erpFlexCustomerId = null, bool $reprocess = false): void
    {
        if (!integration_settings()->enables_customer_automation) {
            return;
        }

        $this->pairing = false;

        if (!integration_settings()->customers_pairing_finished) {
            $this->pairing = true;
        }

        $this->force = $force;
        $this->mercosCustomerService = new MercosCustomerService();

        $this->integrateCustomers($erpFlexCustomerId, $reprocess);
    }

    /**
     * Integrate all customers from ERPFlex to Mercos.
     *
     * @param  int|null $erpFlexCustomerId
     * @param  bool $reprocess
     * @return void
     */
    protected function integrateCustomers(?int $erpFlexCustomerId = null, bool $reprocess = false): void
    {
        if ($reprocess) {
            $this->handleCustomersReprocessing($erpFlexCustomerId);
            return;
        }

        /** @var \App\Models\IntegrationSetting $integrationSetting */
        $integrationSetting = IntegrationSetting::first();
        $executionStart = now();

        $updateStartedAt = Carbon::parse($integrationSetting->last_customers_update)
            ->setTimezone('America/Sao_Paulo');

        SA1::query()
            ->where('SA1_Tipo', '<>', 2)
            ->where('SA1_Campo50', '<>', 'N')
            ->when($erpFlexCustomerId, function (Builder $query) use ($erpFlexCustomerId): Builder {
                return $query->where('SA1_ID', $erpFlexCustomerId);
            })
            ->when(!$this->force, function (Builder $query) use ($updateStartedAt): Builder {
                return $query->where(function (Builder $query) use ($updateStartedAt): Builder {
                    return $query
                        ->where('SA1_DT_INC', '>=', $updateStartedAt)
                        ->orWhere('SA1_DT_ALT', '>=', $updateStartedAt);
                });
            })
            ->lazyById(1000, 'SA1_ID')
            ->each(function (SA1 $erpFlexApiCustomer): void {
                try {
                    $this->processSingleCustomer($erpFlexApiCustomer);
                } catch (Throwable $th) {
                    error($th);
                }
            });

        $integrationSetting->update(['last_customers_update' => $executionStart]);
    }

    /**
     * Hnadle the customers reprocessing.
     *
     * @param  int|null $erpFlexId
     * @return void
     */
    protected function handleCustomersReprocessing(?int $erpFlexId = null): void
    {
        Customer::query()
            ->whereNull('mercos_id')
            ->when(!is_null($erpFlexId), fn (Builder $query): Builder => $query->where('erp_flex_id', $erpFlexId))
            ->get()
            ->each(function (Customer $customer): void {
                try {
                    $this->createSingleCustomer($customer, json_decode(json_encode($customer->erp_flex_data)));
                } catch (Throwable $th) {
                    error($th);
                }
            });
    }

    /**
     * Process a single customer.
     *
     * @param  \App\Models\ErpFlex\SA1 $erpFlexApiCustomer
     * @return void
     */
    protected function processSingleCustomer(mixed $erpFlexApiCustomer): void
    {
        if ($erpFlexApiCustomer->SA1_Campo50 === 'N') {
            return;
        }

        $erpFlexData = [
            'SA1_Desc' => $erpFlexApiCustomer->SA1_Desc ?? '',
            'SA1_Fantasia' => $erpFlexApiCustomer->SA1_Fantasia ?? '',
            'SA1_CPF' => $erpFlexApiCustomer->SA1_CPF ?? '',
            'SA1_Inscr' => $erpFlexApiCustomer->SA1_Inscr ?? '',
            'SA1_SUFRAMA' => $erpFlexApiCustomer->SA1_SUFRAMA ?? '',
            'SA1_End' => $erpFlexApiCustomer->SA1_End ?? '',
            'SA1_Numero' => $erpFlexApiCustomer->SA1_Numero ?? '',
            'SA1_Complemento' => $erpFlexApiCustomer->SA1_Complemento ?? '',
            'SA1_CEP' => $erpFlexApiCustomer->SA1_CEP ?? '',
            'SA1_Bairro' => $erpFlexApiCustomer->SA1_Bairro ?? '',
            'SA1_Mun' => $erpFlexApiCustomer->SA1_Mun ?? '',
            'SA1_Est' => $erpFlexApiCustomer->SA1_Est ?? '',
            'SA1_EMail' => $erpFlexApiCustomer->SA1_EMail ?? '',
            'SA1_EMailLOJA' => $erpFlexApiCustomer->SA1_EMailLOJA ?? '',
            'SA1_EMailDANFE' => $erpFlexApiCustomer->SA1_EMailDANFE ?? '',
            'SA1_EMailCobranca' => $erpFlexApiCustomer->SA1_EMailCobranca ?? '',
            'SA1_DDDCom' => $erpFlexApiCustomer->SA1_DDDCom ?? '',
            'SA1_TelCom' => $erpFlexApiCustomer->SA1_TelCom ?? '',
            'SA1_DDDRes' => $erpFlexApiCustomer->SA1_DDDRes ?? '',
            'SA1_TelRes' => $erpFlexApiCustomer->SA1_TelRes ?? '',
            'SA1_DDDCel' => $erpFlexApiCustomer->SA1_DDDCel ?? '',
            'SA1_TelCel' => $erpFlexApiCustomer->SA1_TelCel ?? '',
            'SA1_Tipo' => $erpFlexApiCustomer->SA1_Tipo ?? '',
            'SA1_STATUS' => $erpFlexApiCustomer->SA1_STATUS ?? '',
            'SA1_SitCadastro' => $erpFlexApiCustomer->SA1_SitCadastro ?? '',
            'SA1_Campo50' => $erpFlexApiCustomer->SA1_Campo50 ?? '',
        ];

        if ($this->pairing) {
            CustomerPairing::updateOrCreate([
                'erp_flex_id' => $erpFlexApiCustomer->SA1_ID
            ], [
                'erp_flex_data' => $erpFlexData
            ]);

            return;
        }

        $customer = Customer::updateOrCreate([
            'erp_flex_id' => $erpFlexApiCustomer->SA1_ID
        ], [
            'erp_flex_data' => $erpFlexData
        ]);

        if (!$customer->mercos_id) {
            $this->createSingleCustomer($customer, $erpFlexApiCustomer);
            return;
        }

        $this->updateSingleCustomer($customer, $erpFlexApiCustomer);
    }

    /**
     * Create a single customer.
     *
     * @param  \App\Models\Customer $customer
     * @param  \App\Models\ErpFlex\SA1 $erpFlexApiCustomer
     * @return void
     */
    protected function createSingleCustomer(Customer $customer, mixed $erpFlexApiCustomer): void
    {
        $mercosCustomerObject = new MercosCustomer(
            razao_social: trim($erpFlexApiCustomer->SA1_Desc),
            nome_fantasia: is_null($erpFlexApiCustomer->SA1_Fantasia) || trim($erpFlexApiCustomer->SA1_Fantasia) === ''
                ? trim($erpFlexApiCustomer->SA1_Desc)
                : trim($erpFlexApiCustomer->SA1_Fantasia),
            cnpj: trim(Str::remove(['.', '/', '-'], $erpFlexApiCustomer->SA1_CPF)),
            tipo: strlen(trim(Str::remove(['.', '/', '-'], $erpFlexApiCustomer->SA1_CPF))) > 11
                ? 'J'
                : 'F',
            inscricao_estadual: trim($erpFlexApiCustomer->SA1_Inscr),
            suframa: trim($erpFlexApiCustomer->SA1_SUFRAMA),
            rua: trim($erpFlexApiCustomer->SA1_End),
            numero: trim($erpFlexApiCustomer->SA1_Numero),
            complemento: trim($erpFlexApiCustomer->SA1_Complemento),
            cep: trim($erpFlexApiCustomer->SA1_CEP),
            bairro: trim($erpFlexApiCustomer->SA1_Bairro),
            cidade: trim($erpFlexApiCustomer->SA1_Mun),
            estado: trim($erpFlexApiCustomer->SA1_Est),
            emails: $this->buildEmailsData($erpFlexApiCustomer),
            telefones: $this->buildPhonesData($erpFlexApiCustomer)
        );

        $response = $this->mercosCustomerService->create($mercosCustomerObject);

        sleep($response->delayForRateLimiting);

        if (is_null($response->mercosId)) {
            $response = $this->mercosCustomerService->create($mercosCustomerObject);
        }

        $customer->update([
            'mercos_id' => $response->mercosId
        ]);
    }

    /**
     * Update a single customer.
     *
     * @param  \App\Models\Customer $customer
     * @param  mixed $erpFlexApiCustomer
     * @return void
     */
    protected function updateSingleCustomer(Customer $customer, mixed $erpFlexApiCustomer): void
    {
        $mercosCustomerObject = new MercosCustomer(
            id: $customer->mercos_id,
            razao_social: trim($erpFlexApiCustomer->SA1_Desc),
            nome_fantasia: is_null($erpFlexApiCustomer->SA1_Fantasia) || trim($erpFlexApiCustomer->SA1_Fantasia) === ''
                ? trim($erpFlexApiCustomer->SA1_Desc)
                : trim($erpFlexApiCustomer->SA1_Fantasia),
            cnpj: trim(Str::remove(['.', '/', '-'], $erpFlexApiCustomer->SA1_CPF)),
            tipo: strlen(trim(Str::remove(['.', '/', '-'], $erpFlexApiCustomer->SA1_CPF))) > 11
                ? 'J'
                : 'F',
            inscricao_estadual: trim($erpFlexApiCustomer->SA1_Inscr),
            suframa: trim($erpFlexApiCustomer->SA1_SUFRAMA),
            rua: trim($erpFlexApiCustomer->SA1_End),
            numero: trim($erpFlexApiCustomer->SA1_Numero),
            complemento: trim($erpFlexApiCustomer->SA1_Complemento),
            cep: trim($erpFlexApiCustomer->SA1_CEP),
            bairro: trim($erpFlexApiCustomer->SA1_Bairro),
            cidade: trim($erpFlexApiCustomer->SA1_Mun),
            estado: trim($erpFlexApiCustomer->SA1_Est),
            emails: $this->buildEmailsData($erpFlexApiCustomer),
            telefones: $this->buildPhonesData($erpFlexApiCustomer)
        );

        $response = $this->mercosCustomerService->update($customer->mercos_id, $mercosCustomerObject);

        sleep($response->delayForRateLimiting);

        if (!$response->success) {
            $response = $this->mercosCustomerService->update($customer->mercos_id, $mercosCustomerObject);
        }
    }

    /**
     * Build the emails data.
     *
     * @param  \App\Models\ErpFlex\SA1 $erpFlexApiCustomer
     * @return array
     */
    protected function buildEmailsData(mixed $erpFlexApiCustomer): array
    {
        $emails = [];

        if (filter_var(str_replace(';', '', $erpFlexApiCustomer->SA1_EMail), FILTER_VALIDATE_EMAIL) !== false) {
            $emails[] = ['email' => str_replace(';', '', trim($erpFlexApiCustomer->SA1_EMail))];
        }

        if (filter_var(str_replace(';', '', $erpFlexApiCustomer->SA1_EMailDANFE), FILTER_VALIDATE_EMAIL) !== false) {
            $emails[] = ['email' => str_replace(';', '', trim($erpFlexApiCustomer->SA1_EMailDANFE))];
        }

        if (filter_var(str_replace(';', '', $erpFlexApiCustomer->SA1_EMailCobranca), FILTER_VALIDATE_EMAIL) !== false) {
            $emails[] = ['email' => str_replace(';', '', trim($erpFlexApiCustomer->SA1_EMailCobranca))];
        }

        if (filter_var(str_replace(';', '', $erpFlexApiCustomer->SA1_EMailLOJA), FILTER_VALIDATE_EMAIL) !== false) {
            $emails[] = ['email' => str_replace(';', '', trim($erpFlexApiCustomer->SA1_EMailLOJA))];
        }

        return $emails;
    }

    /**
     * Build the phones data.
     *
     * @param  \App\Models\ErpFlex\SA1 $erpFlexApiCustomer
     * @return array
     */
    protected function buildPhonesData(mixed $erpFlexApiCustomer): array
    {
        $phones = [];

        if ($erpFlexApiCustomer->SA1_TelCom !== '') {
            $phones[] = [
                'numero' => substr(trim($erpFlexApiCustomer->SA1_DDDCom), -2) . trim($erpFlexApiCustomer->SA1_TelCom)
            ];
        }

        if ($erpFlexApiCustomer->SA1_TelRes !== '') {
            $phones[] = [
                'numero' => substr(trim($erpFlexApiCustomer->SA1_DDDRes), -2) . trim($erpFlexApiCustomer->SA1_TelRes)
            ];
        }

        if ($erpFlexApiCustomer->SA1_TelCel !== '') {
            $phones[] = [
                'numero' => substr(trim($erpFlexApiCustomer->SA1_DDDCel), -2) . trim($erpFlexApiCustomer->SA1_TelCel)
            ];
        }

        return $phones;
    }
}
