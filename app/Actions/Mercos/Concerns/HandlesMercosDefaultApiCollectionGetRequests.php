<?php

namespace App\Actions\Mercos\Concerns;

use App\Http\Integrations\Mercos\Services\Interfaces\Getable;
use Illuminate\Support\Collection;

trait HandlesMercosDefaultApiCollectionGetRequests
{
    protected function getMercosApiCollection(Getable $serviceInstance, ?string $lastUpdatedAt = null): Collection
    {
        if ($this->force) {
            $lastUpdatedAt = null;
        }

        $mercosApiResponse = $serviceInstance->get(
            !is_null($lastUpdatedAt)
                ? "alterado_apos=$lastUpdatedAt"
                : null
        );

        if (!$mercosApiResponse->success) {
            sleep($mercosApiResponse->delayForRateLimiting);

            $mercosApiResponse = $serviceInstance->get(
                !is_null($lastUpdatedAt)
                    ? "alterado_apos=$lastUpdatedAt"
                    : null
            );
        }

        $collection = collect($mercosApiResponse->content);
        $extrasRequests = $mercosApiResponse->extrasRequests;

        if ($extrasRequests >= 1) {
            for ($i = 0; $i < $extrasRequests; $i++) {
                $lastItem = end($mercosApiResponse->content);
                $lastUpdatedAt = $lastItem->ultima_alteracao ?? null;

                if (is_null($lastUpdatedAt)) {
                    break;
                }

                sleep($mercosApiResponse->delayForRateLimiting);

                $mercosApiResponse = $serviceInstance->get("alterado_apos=$lastUpdatedAt");

                if (!$mercosApiResponse->success) {
                    sleep($mercosApiResponse->delayForRateLimiting);
                    $mercosApiResponse = $serviceInstance->get("alterado_apos=$lastUpdatedAt");
                }

                $collection = $collection->merge($mercosApiResponse->content);
            }
        }

        return $collection;
    }
}
