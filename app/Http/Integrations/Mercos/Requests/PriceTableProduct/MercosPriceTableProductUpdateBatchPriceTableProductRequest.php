<?php

namespace App\Http\Integrations\Mercos\Requests\PriceTableProduct;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosBatchUpdatePriceTableProduct;
use App\Http\Integrations\Mercos\Requests\MercosBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosPriceTableProductUpdateBatchPriceTableProductRequest extends MercosBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosBatchUpdatePriceTableProduct $mercosBatchUpdatePriceTableProduct
     */
    public function __construct(protected MercosBatchUpdatePriceTableProduct $mercosBatchUpdatePriceTableProduct)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/produtos_tabela_preco_em_lote';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return $this->mercosBatchUpdatePriceTableProduct->toArray();
    }
}
