<?php

namespace App\Models\ErpFlex;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

 /**
 * SB2 (Products and services) Model.
 *
 * @package App\Models\ErpFlex;
 *
 * @property  int $SB2_ID
 * @property  string $SB2_Codigo
 */
class SB2 extends BaseModel
{
    protected $table = 'SB2';
    protected $primaryKey = 'SB2_ID';
    protected $prefix = 'SB2';

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'SB2_DT_INC' => 'datetime',
        'SB2_DT_ALT' => 'datetime',
    ];

    public function sb1(): BelongsTo
    {
        return $this->belongsTo(SB1::class, 'SB2_IDSB1', 'SB1_ID');
    }

    public function sb2Sbw(): HasMany
    {
        return $this->hasMany(SB2_SBW::class, 'SB2_SBW_IDSB2', 'SB2_ID');
    }

    public function sb2DescItensVar(): HasOne
    {
        return $this->hasOne(SB2DescItensVar::class, 'SB2_DESCITENSVAR_IDSB2', 'SB2_ID');
    }

    public function sbp(): HasMany
    {
        return $this->hasMany(SBP::class, 'SBP_IDSB2', 'SB2_ID');
    }
}
