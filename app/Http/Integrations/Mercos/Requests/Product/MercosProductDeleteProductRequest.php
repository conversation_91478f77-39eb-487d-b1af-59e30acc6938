<?php

namespace App\Http\Integrations\Mercos\Requests\Product;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosProduct;
use App\Http\Integrations\Mercos\Requests\MercosBasePutRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosProductDeleteProductRequest extends MercosBasePutRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosProduct $mercosProduct
     */
    public function __construct(protected int $id, protected MercosProduct $mercosProduct)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/produtos/' . $this->id;
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return [
            'nome' => $this->mercosProduct->nome,
            'preco_tabela' => $this->mercosProduct->preco_tabela,
            'excluido' => true
        ];
    }
}
