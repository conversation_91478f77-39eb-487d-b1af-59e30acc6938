<?php

namespace App\Models\Concerns\Category;

use App\Models\Subcategory;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesCategoryRelationships
{
    /**
     * Load the subcategories relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function subcategories(): HasMany
    {
        return $this->hasMany(Subcategory::class);
    }
}
