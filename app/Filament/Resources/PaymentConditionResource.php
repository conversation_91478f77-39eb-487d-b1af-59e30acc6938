<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PaymentConditionResource\Concerns\HandlesPaymentConditionResourceForm;
use App\Filament\Resources\PaymentConditionResource\Concerns\HandlesPaymentConditionResourceTable;
use App\Filament\Resources\PaymentConditionResource\Pages;
use App\Filament\Resources\PaymentConditionResource\RelationManagers;
use App\Models\PaymentCondition;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PaymentConditionResource extends Resource
{
    use HandlesPaymentConditionResourceForm;
    use HandlesPaymentConditionResourceTable;

    protected static ?string $model = PaymentCondition::class;
    protected static ?string $modelLabel = 'forma de pagamento';
    protected static ?string $pluralModelLabel = 'formas de pagamento';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManagePaymentConditions::route('/'),
        ];
    }
}
