<?php

namespace App\Support;

class PhoneParser
{
    /**
     * Formats and validates phone structure.
     *
     * @param  string|null $phoneNumber
     * @return array
     */
    public static function parsePhoneNumber(string $phoneNumber = null): array
    {
        if (is_null($phoneNumber)) {
            return ['code_area' => null, 'number' => null, 'type' => 'invalid'];
        }

        $phoneNumber = preg_replace("/[^0-9]/", "", $phoneNumber);

        if (substr($phoneNumber, 0, 2) === "55") {
            $phoneNumber = substr($phoneNumber, 2);
        }

        $type = (strlen($phoneNumber) == 10) ? 'phone' : ((strlen($phoneNumber) == 11) ? 'cellphone' : '');

        if ($type === '') {
            return ['code_area' => null, 'number' => null, 'type' => 'invalid'];
        }

        $codeArea = substr($phoneNumber, 0, 2);
        $number = substr($phoneNumber, 2);

        return [
            'code_area' => $codeArea,
            'number' => $number,
            'type' => $type,
        ];
    }
}
