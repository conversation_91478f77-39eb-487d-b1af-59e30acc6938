<?php

namespace App\Actions\Mercos;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosBatchUpdatePriceTableProduct;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosSinglePriceTableProduct;
use App\Http\Integrations\Mercos\Services\MercosPriceTableProductService;
use App\Models\ErpFlex\SBP;
use App\Models\PriceTable;
use App\Models\IntegrationSetting;
use App\Models\PriceTableProduct;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreatePriceTableProductFromErpFlex
{
    use AsAction;

    protected bool $force = false;
    protected array $sb2ProductCache = [];
    protected array $priceTableCache = [];

    protected $priceTables;
    protected MercosPriceTableProductService $mercosPriceTableProductService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue('erp_flex_price_' . tenant('id'));
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_price_table_automation) {
            return;
        }

        $this->force = $force;
        $this->mercosPriceTableProductService = new MercosPriceTableProductService();

        $this->integratePriceTableItems();
    }

    /**
     * Integrate all price table items from ERPFlex to Mercos.
     *
     * @return void
     */
    protected function integratePriceTableItems(): void
    {
        /** @var \App\Models\IntegrationSetting $integrationSetting */
        $integrationSetting = IntegrationSetting::first();
        $executionStart = now();

        $updateStartedAt = Carbon::parse($integrationSetting->last_price_table_products_update)
            ->setTimezone('America/Sao_Paulo');

        // Load the paired price tables.
        $this->priceTables = PriceTable::query()
            ->whereNotNull('mercos_id')
            ->get();

        SBP::query()
            ->when(!$this->force, function (Builder $query) use ($updateStartedAt): Builder {
                return $query->where(function (Builder $query) use ($updateStartedAt): Builder {
                    return $query
                        ->where('SBP_DT_INC', '>=', $updateStartedAt)
                        ->orWhere('SBP_DT_ALT', '>=', $updateStartedAt);
                });
            })
            ->chunk(250, function (Collection $sbpGroup): void {
                try {
                    $this->processPriceTableProductBatch($sbpGroup);
                } catch (Throwable $th) {
                    error($th);
                }
            });

        $integrationSetting->update(['last_price_table_products_update' => $executionStart]);
    }

    /**
     * Realize the batch adjustment for the collection.
     *
     * @param  \Illuminate\Support\Collection $sbpGroup
     * @return void
     */
    protected function processPriceTableProductBatch(Collection $sbpGroup): void
    {
        $batchSbpArray = [];

        $sbpGroup->each(function (SBP $sbp) use (&$batchSbpArray): void {
            $product = $this->getProduct($sbp);

            if (is_null($product)) {
                error($sbp->SBP_IDSB2 . ' - create_price_table_product_from_erp_flex.product_not_found');
                return;
            }

            $priceTable = $this->priceTables
                ->where('erp_flex_table_id', (string) $sbp->SBP_Tabela)
                ->first();

            if (is_null($priceTable)) {
                error($sbp->SBP_ID . ' - create_price_table_product_from_erp_flex.price_table_not_found');
                return;
            }

            if ($priceTable->erp_flex_table_id === '0') {
                return;
            }

            /** @var \App\Models\PriceTableProduct $priceItem */
            $priceItem = PriceTableProduct::updateOrCreate(
                ['erp_flex_id' => $sbp->SBP_ID],
                [
                    'erp_flex_data' => [
                        'SBP_ID' => $sbp->SBP_ID,
                        'SBP_Tabela' => $sbp->SBP_Tabela,
                        'SBP_Preco' => $sbp->SBP_Preco,
                        'SBP_IDSB1' => $sbp->SBP_IDSB1,
                        'SBP_IDSB2' => $sbp->SBP_IDSB2,
                        'SBP_DT_INC' => $sbp->SBP_DT_INC,
                        'SBP_DT_ALT' => $sbp->SBP_DT_ALT,
                    ]
                ],
            );

            $batchSbpArray[] = new MercosSinglePriceTableProduct(
                produto_id: $product->mercos_id,
                tabela_id: $priceTable->mercos_id,
                preco: $sbp->SBP_Preco ?? 0,
                id: $priceItem->mercos_id
            );
        });

        $mercosBatchUpdatePriceTableProduct = new MercosBatchUpdatePriceTableProduct($batchSbpArray);

        $response = $this->mercosPriceTableProductService->handleBatch($mercosBatchUpdatePriceTableProduct);

        sleep($response->delayForRateLimiting);

        if (!$response->success) {
            $response = $this->mercosPriceTableProductService->handleBatch($mercosBatchUpdatePriceTableProduct);
        }

        $this->mapMercosIds($response->data);
    }

    /**
     * Map the Mercos IDs.
     *
     * @param  array $mercosPriceTableProductData
     * @return void
     */
    protected function mapMercosIds(array $mercosPriceTableProductData): void
    {
        collect($mercosPriceTableProductData)->each(function (array $mercosPriceTableProduct): void {
            // Get SB2 ID from cache or database.
            if (!isset($this->sb2ProductCache[$mercosPriceTableProduct['produto_id']])) {
                /** @var \App\Models\Product $product */
                $product = Product::query()
                    ->where('mercos_id', $mercosPriceTableProduct['produto_id'])
                    ->first();

                $this->sb2ProductCache[$mercosPriceTableProduct['produto_id']] = $product->erp_flex_id;
            }

            $sb2Id = $this->sb2ProductCache[$mercosPriceTableProduct['produto_id']];

            // Get the ERPFlex table code from cache or database.
            if (!isset($this->priceTableCache[$mercosPriceTableProduct['tabela_id']])) {
                /** @var \App\Models\PriceTable $priceTable */
                $priceTable = PriceTable::query()
                    ->where('mercos_id', $mercosPriceTableProduct['tabela_id'])
                    ->first();

                $this->priceTableCache[$mercosPriceTableProduct['tabela_id']] = $priceTable->erp_flex_table_id;
            }

            $erpFlexTableCode = $this->priceTableCache[$mercosPriceTableProduct['tabela_id']];

            PriceTableProduct::query()
                ->whereJsonContains('erp_flex_data->SBP_IDSB2', $sb2Id)
                ->whereJsonContains('erp_flex_data->SBP_Tabela', $erpFlexTableCode)
                ->get()
                ->each(function (PriceTableProduct $priceTableProduct) use ($mercosPriceTableProduct): void {
                    if ($priceTableProduct->mercos_id) {
                        return;
                    }

                    $priceTableProduct->update(['mercos_id' => $mercosPriceTableProduct['id']]);
                });
        });
    }

    /**
     * Gets a product from a sbp object.
     *
     * @param SBP $sbp
     * @return Product|null
     */
    protected function getProduct(SBP $sbp): Product|null
    {
        if (is_null($sbp->sb2->SB2_ID)) {
            return null;
        }

        return Product::query()
            ->where('erp_flex_id', $sbp->sb2->SB2_ID)
            ->first();
    }
}
