<?php

namespace App\Models\ErpFlex;

use Illuminate\Database\Eloquent\Relations\HasMany;

 /**
 * SC5 (Orders) Model.
 *
 * @package App\Models\ErpFlex;
 *
 * @property  int $SC5_Tabela
 * @property  int $SC5_Hist
 */
class SC5 extends BaseModel
{
    protected $table = 'SC5';
    protected $primaryKey = 'SC5_ID';
    protected $prefix = 'SC5';

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'SC5_DT_INC' => 'datetime',
        'SC5_DT_ALT' => 'datetime',
    ];

    public function sc6s(): HasMany
    {
        return $this->hasMany(SC6::class, 'SC6_IDSC5', 'SC5_ID');
    }
}
