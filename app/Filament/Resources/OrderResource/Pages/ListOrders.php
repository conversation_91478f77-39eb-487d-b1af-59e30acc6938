<?php

namespace App\Filament\Resources\OrderResource\Pages;

use App\Filament\Resources\OrderResource;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Artisan;
use Throwable;

class ListOrders extends ListRecords
{
    protected static string $resource = OrderResource::class;

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            Action::make('Forçar sincronização')
                ->action(function (): void {
                    try {
                        Artisan::call(SyncOrders::class, ['force' => true]);
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->color('warning')
                ->requiresConfirmation(),
            Action::make('Sincronizar')
                ->action(function (): void {
                    try {
                        Artisan::call(SyncOrders::class);
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->requiresConfirmation(),
        ];
    }
}
