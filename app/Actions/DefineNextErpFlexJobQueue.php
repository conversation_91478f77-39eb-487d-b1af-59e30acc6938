<?php

namespace App\Actions;

use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class DefineNextErpFlexJobQueue
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @return string
     */
    public function handle(string $queuePostfix): string
    {
        $queue = DB::connection(config('tenancy.database.central_connection'))
            ->table('predefined_erp_flex_queues')
            ->leftJoin('jobs', 'jobs.queue', 'predefined_erp_flex_queues.queue')
            ->select([
                'predefined_erp_flex_queues.queue',
                DB::raw('count(1) as queue_count'),
            ])
            ->where('predefined_erp_flex_queues.queue', 'like', "erp_flex_{$queuePostfix}_%")
            ->groupBy('predefined_erp_flex_queues.queue')
            ->orderBy('queue_count')
            ->first();

        return !is_null($queue)
            ? $queue->queue
            : "erp_flex_{$queuePostfix}_1";
    }
}
