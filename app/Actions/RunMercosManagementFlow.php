<?php

namespace App\Actions;

use App\Actions\ErpFlex\CreateCustomersFromMercos;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class RunMercosManagementFlow
{
    use AsAction;

    public string $commandSignature = 'efm:run-mercos-management-flow {tenant?} {force?}';
    public string $commandDescription = 'Runs the Mercos > ERPFlex management\' flow.';

    public function asCommand(Command $command): void
    {
        $this->handle(
            tenant: $command->argument('tenant'),
            force: (bool) $command->argument('force')
        );
    }

    public function handle(?string $tenant = null, bool $force = false): void
    {
        $tenants = Tenant::query()
            ->when($tenant, fn (Builder $query): Builder => $query->where('id', $tenant))
            ->get();

        tenancy()->runForMultiple($tenants, function () use ($force) {
            CreateCustomersFromMercos::dispatch($force, null, true);
        });
    }
}
