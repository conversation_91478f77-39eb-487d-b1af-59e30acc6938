<?php

namespace App\Http\Integrations\Mercos\Requests\Salesman;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosSalesman;
use App\Http\Integrations\Mercos\Requests\MercosBasePutRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosSalesmanUpdateSalesmanRequest extends MercosBasePutRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosSalesman $mercosSalesmen
     */
    public function __construct(
        protected int $id,
        protected MercosSalesman $mercosSalesmen
    ) {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/usuarios/' . $this->id;
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return [
            'nome' => $this->mercosSalesmen->nome,
        ];
    }
}
