<?php

namespace App\Actions\Pairing;

use App\Http\Integrations\Mercos\Services\MercosCustomerService;
use App\Models\Customer;
use App\Models\CustomerPairing as CustomerPairingModel;
use App\Models\IntegrationSetting;
use Exception;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CustomerPairing
{
    use AsAction;

    protected MercosCustomerService $mercosCustomerService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(tenant('id') . '_cadastros');
    }

    /**
     * Handle the action.
     *
     * @param boolean $mercosOriginFlow
     * @return void
     */
    public function handle(bool $mercosOriginFlow = false): void
    {
        try {
            if (integration_settings()->customers_pairing_finished) {
                throw new Exception('customers_pairing.pairing_should_only_be_done_once');
                return;
            }

            $pairingCollection = CustomerPairingModel::get();

            $pairingCollection->each(function (CustomerPairingModel $pairing) use ($mercosOriginFlow): void {
                if ($mercosOriginFlow) {
                    if (!$pairing->erp_flex_id) {
                        return;
                    }
                } else {
                    if (!$pairing->mercos_id) {
                        return;
                    }
                }

                Customer::create([
                    'erp_flex_id' => $pairing->erp_flex_id,
                    'erp_flex_data' => $pairing->erp_flex_data,
                    'mercos_id' => $pairing->mercos_id,
                    'mercos_data' => $pairing->mercos_data,
                ]);
            });

            $integrationSetting = IntegrationSetting::first();
            $integrationSetting->customers_pairing_finished = true;
            $integrationSetting->save();
        } catch (Throwable $th) {
            error($th);
        }
    }
}
