<?php

namespace App\Http\Integrations\Mercos\Requests\Order;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderReceivable;
use App\Http\Integrations\Mercos\Requests\MercosBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosCreateReceivableRequest extends MercosBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderReceivable $mercosOrderReceivable
     */
    public function __construct(protected MercosOrderReceivable $mercosOrderReceivable)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/titulos';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return [
            'cliente_id' => $this->mercosOrderReceivable->cliente_id,
            'numero_documento' => $this->mercosOrderReceivable->numero_documento,
            'valor' => $this->mercosOrderReceivable->valor,
            'data_vencimento' => $this->mercosOrderReceivable->data_vencimento,
            'pedido_id' => $this->mercosOrderReceivable->pedido_id,
            'arquivo_pdf' => $this->mercosOrderReceivable->arquivo_pdf,
            'data_pagamento' => $this->mercosOrderReceivable->data_pagamento,
            'observacao' => $this->mercosOrderReceivable->observacao,
            'link_pdf' => $this->mercosOrderReceivable->link_pdf,
            'excluido' => is_null($this->mercosOrderReceivable->excluido)
                ? false
                : $this->mercosOrderReceivable->excluido,
        ];
    }
}
