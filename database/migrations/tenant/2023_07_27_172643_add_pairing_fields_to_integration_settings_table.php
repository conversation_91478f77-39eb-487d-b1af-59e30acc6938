<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('integration_settings', function (Blueprint $table) {
            $table->addColumn('boolean', 'customers_pairing_finished')->default(false);
            $table->addColumn('boolean', 'categories_pairing_finished')->default(false);
            $table->addColumn('boolean', 'subcategories_pairing_finished')->default(false);
            $table->addColumn('boolean', 'products_pairing_finished')->default(false);
            $table->addColumn('boolean', 'hauliers_pairing_finished')->default(false);
            $table->addColumn('boolean', 'payment_conditions_pairing_finished')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('integration_settings', function (Blueprint $table) {
            $table->dropColumn('customers_pairing_finished');
            $table->dropColumn('categories_pairing_finished');
            $table->dropColumn('subcategories_pairing_finished');
            $table->dropColumn('products_pairing_finished');
            $table->dropColumn('hauliers_pairing_finished');
            $table->dropColumn('payment_conditions_pairing_finished');
        });
    }
};
