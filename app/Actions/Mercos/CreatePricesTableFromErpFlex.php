<?php

namespace App\Actions\Mercos;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTable;
use App\Http\Integrations\Mercos\Services\MercosPriceTableService;
use App\Models\ErpFlex\SX6;
use App\Models\PriceTable;
use App\Models\IntegrationSetting;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreatePricesTableFromErpFlex
{
    use AsAction;

    protected bool $force = false;

    protected MercosPriceTableService $mercosPriceTableService;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue('erp_flex_price_' . tenant('id'));
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_price_table_automation) {
            return;
        }

        $this->force = $force;
        $this->mercosPriceTableService = new MercosPriceTableService();

        $this->integratePricesTable();
    }

    /**
     * Integrate all haulier conditions from ERPFlex to Mercos.
     *
     * @return void
     */
    protected function integratePricesTable(): void
    {
        try {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();
            $executionStart = now();

            $updateStartedAt = Carbon::parse($integrationSetting->last_price_table_update)
                ->setTimezone('America/Sao_Paulo');

            SX6::query()
                ->where('SX6_Codigo', 'like', 'TABELAPRECO%')
                ->when(!$this->force, function (Builder $query) use ($updateStartedAt) : Builder {
                    return $query->where(function (Builder $query) use ($updateStartedAt) : Builder {
                        return $query
                            ->where('SX6_DT_INC', '>=', $updateStartedAt)
                            ->orWhere('SX6_DT_ALT', '>=', $updateStartedAt);
                    });
                })
                ->lazyById(1000)
                ->each(function (mixed $erpFlexApiPriceTable): void {
                    $this->processSinglePriceTable($erpFlexApiPriceTable);
                });

            $integrationSetting->update(['last_price_table_update' => $executionStart]);
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Process a single haulier condition.
     *
     * @param  \App\Models\ErpFlex\SX6 $erpFlexApiPriceTable
     * @return void
     */
    protected function processSinglePriceTable(SX6 $erpFlexApiPriceTable): void
    {
        /** @var \App\Models\PriceTable $haulier */
        $haulier = PriceTable::updateOrCreate(
            ['erp_flex_id' => $erpFlexApiPriceTable->SX6_ID],
            [
                'erp_flex_table_id' => str_replace('TABELAPRECO', '', $erpFlexApiPriceTable->SX6_Codigo) ?? null,
                'erp_flex_data' => [
                    'SX6_ID' => $erpFlexApiPriceTable->SX6_ID,
                    'SX6_Codigo' => $erpFlexApiPriceTable->SX6_Codigo,
                    'SX6_Conteudo' => $erpFlexApiPriceTable->SX6_Conteudo,
                    'SX6_DT_INC' => $erpFlexApiPriceTable->SX6_DT_INC,
                    'SX6_DT_ALT' => $erpFlexApiPriceTable->SX6_DT_ALT,
                ]
            ],
        );

        if (!$haulier->mercos_id) {
            $this->createSinglePriceTable($haulier, $erpFlexApiPriceTable);
            return;
        }

        $this->updateSinglePriceTable($haulier, $erpFlexApiPriceTable);
    }

    /**
     * Create a single haulier condition.
     *
     * @param  \App\Models\PriceTable $haulier
     * @param  \App\Models\ErpFlex\SX6 $erpFlexApiPriceTable
     * @return void
     */
    protected function createSinglePriceTable(PriceTable $haulier, SX6 $erpFlexApiPriceTable): void
    {
        $mercosPriceTableObject = new MercosPriceTable(
            nome: textLimit($erpFlexApiPriceTable->SX6_Conteudo),
            tipo: 'P'
        );

        $response = $this->mercosPriceTableService->create($mercosPriceTableObject);

        sleep($response->delayForRateLimiting);

        if (is_null($response->mercosId)) {
            $response = $this->mercosPriceTableService->create($mercosPriceTableObject);
        }

        $haulier->update([
            'mercos_id' => $response->mercosId
        ]);
    }

    /**
     * Update a single haulier condition.
     *
     * @param  \App\Models\PriceTable $haulier
     * @param  mixed $erpFlexApiPriceTable
     * @return void
     */
    protected function updateSinglePriceTable(PriceTable $haulier, SX6 $erpFlexApiPriceTable): void
    {
        $mercosPriceTableObject = new MercosPriceTable(
            id: $haulier->mercos_id,
            nome: textLimit($erpFlexApiPriceTable->SX6_Conteudo),
            tipo: 'P'
        );

        $response = $this->mercosPriceTableService->update($haulier->mercos_id, $mercosPriceTableObject);

        sleep($response->delayForRateLimiting);

        if (!$response->success) {
            $this->mercosPriceTableService->update($haulier->mercos_id, $mercosPriceTableObject);
        }
    }
}
