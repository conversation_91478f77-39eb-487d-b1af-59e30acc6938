<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosProductCategory
{
    /**
     * Create a new instance.
     *
     * @param  string $nome
     * @param  int|null $id
     * @param  string|null $categoria_pai_id
     * @param  bool|null $excluido
     * @param  string|null $ultima_alteracao
     */
    public function __construct(
        public string $nome,
        public ?int $id = null,
        public ?string $categoria_pai_id = null,
        public ?bool $excluido = null,
        public ?string $ultima_alteracao = null
    ) {
    }

    /**
     * Create a new instance from an object.
     *
     * @param  object $object
     * @param  bool $create
     * @return static
     */
    public static function fromObject(object $object, bool $create = false): static
    {
        $instance = new static(
            id: $object->id,
            categoria_pai_id: $object->categoria_pai_id,
            nome: $object->nome,
            excluido: $object->excluido,
            ultima_alteracao: $object->ultima_alteracao
        );

        if ($create) {
            unset($instance->id);
            unset($instance->ultima_alteracao);
        }

        return $instance;
    }

    /**
     * @inheritDoc
     */
    public function toArray(bool $write = false): array
    {
        $data = [
            'id' => $this->id,
            'categoria_pai_id' => $this->categoria_pai_id,
            'nome' => $this->nome,
            'excluido' => $this->excluido,
            'ultima_alteracao' => $this->ultima_alteracao
        ];

        if ($write) {
            unset($data['id']);
            unset($data['ultima_alteracao']);
        }

        return $data;
    }
}
