<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexCustomer;
use App\Http\Integrations\ErpFlex\DataTransferObjects\Response\ErpFlexCustomerResponse;
use App\Http\Integrations\ErpFlex\ErpFlexConnector;
use App\Http\Integrations\ErpFlex\Requests\Customer\ErpFlexCustomerCreateCustomerRequest;
use App\Http\Integrations\ErpFlex\Requests\Customer\ErpFlexCustomerGetCustomersRequest;
use App\Http\Integrations\ErpFlex\Requests\Customer\ErpFlexCustomerUpdateCustomerRequest;

class ErpFlexCustomerService extends ErpFlexBaseService
{
    use HandlesLogCreation;

    /**
     * Get all customers.
     *
     * @param  int|null $limit
     * @param  int|null $offset
     * @param  string|null $filters
     * @return mixed
     */
    public function get(?int $limit = null, ?int $offset = null, ?string $filters = null): mixed
    {
        return json_decode(
            (new ErpFlexConnector($this->username, $this->password))
                ->request(new ErpFlexCustomerGetCustomersRequest($limit, $offset, $filters))
                ->send()
        );
    }

    /**
     * Create a customer.
     *
     * @param  \App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexCustomer $erpFlexCustomer
     * @return \App\Http\Integrations\ErpFlex\DataTransferObjects\Response\ErpFlexCustomerResponse
     */
    public function create(ErpFlexCustomer $erpFlexCustomer): ?ErpFlexCustomerResponse
    {
        $request = new ErpFlexCustomerCreateCustomerRequest($erpFlexCustomer);

        $response = (new ErpFlexConnector($this->username, $this->password))
            ->request($request)
            ->send();

        $body = json_decode($response->body());

        if (is_null($body)) {
            $this->createLog(
                $response,
                false,
                'erp_flex_customer_service.create.empty_body',
                $request->defaultData()
            );

            return null;
        }

        $this->createLog(
            $response,
            ($body->success === 1),
            null,
            $request->defaultData()
        );

        return new ErpFlexCustomerResponse(
            erpFlexId: $body->data->id ?? null,
            success: $body->success === 1,
            message: $body->message ?? null
        );
    }

    /**
     * Update a customer.
     *
     * @param  int|string $id
     * @param  \App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexCustomer $erpFlexCustomer
     * @return \App\Http\Integrations\ErpFlex\DataTransferObjects\Response\ErpFlexCustomerResponse|null
     */
    public function update(int|string $id, ErpFlexCustomer $erpFlexCustomer): ?ErpFlexCustomerResponse
    {
        $request = new ErpFlexCustomerUpdateCustomerRequest($id, $erpFlexCustomer);

        $response = (new ErpFlexConnector($this->username, $this->password))
            ->request($request)
            ->send();

        $body = json_decode($response->body());

        if (is_null($body)) {
            $this->createLog(
                $response,
                false,
                'erp_flex_customer_service.update.empty_body',
                $request->defaultData()
            );

            return null;
        }

        $this->createLog(
            $response,
            ($body->success === 1),
            null,
            $request->defaultData()
        );

        return new ErpFlexCustomerResponse(
            erpFlexId: $body->data->id ?? null,
            success: $body->success === 1,
            message: $body->message ?? null
        );
    }
}
