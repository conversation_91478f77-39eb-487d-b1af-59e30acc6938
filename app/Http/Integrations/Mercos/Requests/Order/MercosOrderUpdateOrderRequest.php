<?php

namespace App\Http\Integrations\Mercos\Requests\Order;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosOrder;
use App\Http\Integrations\Mercos\Requests\MercosBasePutRequest;

class MercosOrderUpdateOrderRequest extends MercosBasePutRequest
{
    /**
     * Create a new instance.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosOrder $mercosOrder
     * @param  int $deliveryAddressId
     * @param  array $itens
     * @param  array $extras
     */
    public function __construct(
        protected int $id,
        protected MercosOrder $mercosOrder,
        protected int $deliveryAddressId,
        protected array $itens,
        protected array $extras = []
    ) {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/api/v2/pedidos/' . $this->id;
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return array_filter([
            'cliente_id' => $this->mercosOrder->cliente_id,
            'data_emissao' => $this->mercosOrder->data_emissao,
            'contato_id' => $this->mercosOrder->contato_id,
            'transportadora_id' => $this->mercosOrder->transportadora_id,
            'condicao_pagamento_id' => $this->mercosOrder->condicao_pagamento_id,
            'tipo_pedido_id' => $this->mercosOrder->tipo_pedido_id,
            'forma_pagamento_id' => $this->mercosOrder->forma_pagamento_id,
            'endereco_entrega_id' => $this->deliveryAddressId,
            'observacoes' => $this->mercosOrder->observacoes,
            'extras' => $this->extras,
            'itens' => $this->itens,
            'data_criacao' => $this->mercosOrder->data_criacao,
            'rastreamento' => $this->mercosOrder->rastreamento,
            'valor_frete' => $this->mercosOrder->valor_frete,
            'criador_id' => $this->mercosOrder->criador_id
        ]);
    }
}
