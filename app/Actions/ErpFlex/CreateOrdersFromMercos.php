<?php

namespace App\Actions\ErpFlex;

use App\Actions\Mercos\Concerns\HandlesMercosDefaultApiCollectionGetRequests;
use App\Dictionaries\MercosOrderStatusDictionary;
use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexCustomer;
use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexOrder;
use App\Http\Integrations\ErpFlex\Services\ErpFlexCustomerService;
use App\Http\Integrations\ErpFlex\Services\ErpFlexOrderService;
use App\Http\Integrations\Mercos\Services\MercosCustomerService;
use App\Http\Integrations\Mercos\Services\MercosOrderService;
use App\Models\Customer;
use App\Models\ErpFlex\SC5;
use App\Models\Haulier;
use App\Models\IntegrationSetting;
use App\Models\Order;
use App\Models\PaymentCondition;
use App\Models\PriceTable;
use App\Models\Product;
use App\Models\Salesman;
use App\Support\PhoneParser;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateOrdersFromMercos
{
    use AsAction;
    use HandlesMercosDefaultApiCollectionGetRequests;

    protected bool $force;

    protected MercosOrderService $mercosOrderService;
    protected ErpFlexOrderService $erpFlexOrderService;
    protected MercosCustomerService $mercosCustomerService;
    protected ErpFlexCustomerService $erpFlexCustomerService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue('mercos_' . tenant('id'));
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @param  int|null $mercosId
     * @param  bool $reprocess
     * @return void
     */
    public function handle(bool $force = false, ?int $mercosId = null, bool $reprocess = false): void
    {
        if (!integration_settings()->enables_orders_automation) {
            return;
        }

        $this->force = $force;

        $this->mercosOrderService = new MercosOrderService();
        $this->erpFlexOrderService = new ErpFlexOrderService();
        $this->mercosCustomerService = new MercosCustomerService();
        $this->erpFlexCustomerService = new ErpFlexCustomerService();

        $this->integrateOrders($mercosId, $reprocess);
    }

    /**
     * Integrate the orders.
     *
     * @param  int|null $mercosId
     * @param  bool $reprocess
     * @return void
     */
    protected function integrateOrders(?int $mercosId = null, bool $reprocess = false): void
    {
        try {
            $executionStart = now();
            $integrationSetting = integration_settings();
            $mercosOrders = $this->getOrdersCollection($integrationSetting);
        } catch (Throwable $th) {
            throw_error($th);
        }

        foreach ($mercosOrders as $mercosOrder) {
            if ((int) $mercosOrder->status !== MercosOrderStatusDictionary::ORDER_COMPLETE) {
                continue;
            }

            if (!is_null($mercosId) && (int) $mercosOrder->id !== $mercosId) {
                continue;
            }

            sleep(1);

            try {
                $this->processSingleOrder($mercosOrder);
            } catch (Throwable $th) {
                error($th);
            }
        }

        if ($reprocess) {
            $this->handleOrdersReprocessing($mercosId);
        }

        $integrationSetting->update(['last_orders_update' => $executionStart]);
    }

    /**
     * Handle the orders reprocessing.
     *
     * @param  int|null $mercosId
     * @return void
     */
    protected function handleOrdersReprocessing(?int $mercosId = null): void
    {
        Order::query()
            ->whereNull('erp_flex_id')
            ->when(!is_null($mercosId), fn (Builder $query): Builder => $query->where('mercos_id', $mercosId))
            ->get()
            ->each(function (Order $order): void {
                try {
                    $this->createSingleOrder($order, json_decode(json_encode($order->mercos_data)));
                } catch (Throwable $th) {
                    error($th);
                }

                sleep(1);
            });
    }

    /**
     * Get the orders collection for the mercos API.
     *
     * @param  \App\Models\IntegrationSetting $integrationSetting
     * @return \Illuminate\Support\Collection
     */
    protected function getOrdersCollection(IntegrationSetting $integrationSetting): Collection
    {
        $lastUpdatedAt = Carbon::parse($integrationSetting->last_orders_update)
            ->setTimezone('America/Sao_Paulo')
            ->format('Y-m-d H:i:s');

        return $this->getMercosApiCollection($this->mercosOrderService, $lastUpdatedAt);
    }

    /**
     * Process a single order.
     *
     * @param  mixed $mercosOrder
     * @return void
     */
    protected function processSingleOrder(mixed $mercosOrder): void
    {
        /** @var \App\Models\Order $order */
        $order = Order::updateOrCreate(
            ['mercos_id' => $mercosOrder->id],
            ['mercos_data' => json_decode(json_encode($mercosOrder), true)]
        );

        if ($order->erp_flex_id) {
            return;
        }

        $this->createSingleOrder($order, $mercosOrder);
    }

    /**
     * Create a single order.
     *
     * @param  \App\Models\Order $order
     * @param  mixed $mercosOrder
     * @return void
     */
    protected function createSingleOrder(Order $order, mixed $mercosOrder): void
    {
        /** @var \App\Models\Customer $customer */
        $customer = Customer::query()
            ->where('mercos_id', $mercosOrder->cliente_id)
            ->whereNotNull('erp_flex_id')
            ->first();

        if (!$customer) {
            $this->createSingleCustomer($mercosOrder);
        } else {
            $this->updateSingleCustomer($customer, $mercosOrder);
        }

        $items = [];

        collect($mercosOrder->itens)->each(function (mixed $item) use (&$items): void {
            if ($item->excluido === true) {
                return;
            }

            $product = Product::query()
                ->where('mercos_id', $item->produto_id)
                ->first();

            $items[] = array_filter([
                'produto_id' => $product->erp_flex_data['sb1']['SB1_ID'],
                'variante_id' => (int) $product->erp_flex_id,
                'quantidade' => $item->quantidade,
                'preco_unitario' => $item->preco_liquido,
                'ipi_taxa' => $item->tipo_ipi === 'P'
                    ? ($item->ipi / 100)
                    : 0,
                'valicms_st' => isset($item->st)
                    ? ($item->quantidade * (($item->preco_liquido * (1 + ($item->st / 100))) - $item->preco_liquido))
                    : 0,
            ]);
        });

        $firstItem = collect($mercosOrder->itens)->first();

        if (is_null($order->erp_flex_id)) {
            /** @var \App\Models\ErpFlex\SC5 $sc5 */
            $sc5 = SC5::query()
                ->where('SC5_Doc', (string) $mercosOrder->numero)
                ->where('SC5_Hist', 'INTEGRAÇÃO MERCOS - ID: ' . $mercosOrder->numero)
                ->first();

            if (!is_null($sc5)) {
                $order->update(['erp_flex_id' => $sc5->SC5_ID]);
                return;
            }
        }

        $erpFlexOrder = new ErpFlexOrder(
            documento: $mercosOrder->numero,
            emissao: Carbon::parse($mercosOrder->data_emissao)
                ->format('d/m/Y'),
            cliente_id: Customer::query()
                ->where('mercos_id', $mercosOrder->cliente_id)
                ->first()
                ->erp_flex_id,
            vendedor_id: Salesman::query()
                ->where('mercos_id', $mercosOrder->criador_id)
                ->first()
                ->erp_flex_id,
            transportadora: Haulier::query()
                ->where('mercos_id', $mercosOrder->transportadora_id)
                ->first()
                ?->erp_flex_id ?? null,
            historico: 'INTEGRAÇÃO MERCOS - ID: ' . $mercosOrder->numero,
            formas_pags_id: PaymentCondition::query()
                ->where('mercos_id', $mercosOrder->condicao_pagamento_id)
                ->first()
                ->erp_flex_id,
            valor_frete: $mercosOrder->valor_frete ?? 0,
            itens: json_encode($items),
            campo1: $mercosOrder->observacoes,
            campo2: $mercosOrder->condicao_pagamento,
            tabela_preco: $firstItem->tabela_preco_id !== 0 && !is_null($firstItem->tabela_preco_id)
                ? PriceTable::query()->where('mercos_id', $firstItem->tabela_preco_id)->first()->erp_flex_table_id
                : null
        );

        $order->update([
            'erp_flex_id' => $this->erpFlexOrderService->create($erpFlexOrder)
        ]);
    }

    /**
     * Create a single customer.
     *
     * @param  mixed $mercosOrder
     * @return void
     */
    protected function createSingleCustomer(mixed $mercosOrder): void
    {
        $erpFlexCustomerObject = new ErpFlexCustomer(
            nome: $mercosOrder->cliente_razao_social ?? null,
            fantasia: $mercosOrder->cliente_nome_fantasia ?? null,
            cpf_cnpj: $mercosOrder->cliente_cnpj ?? null,
            inscricao_estadual: $mercosOrder->cliente_inscricao_estadual ?? null,
            endereco: $mercosOrder->cliente_rua ?? null,
            numero: $mercosOrder->cliente_numero ?? null,
            complemento: $mercosOrder->cliente_complemento ?? null,
            cep: $mercosOrder->cliente_cep ?? null,
            bairro: $mercosOrder->cliente_bairro ?? null,
            municipio: $mercosOrder->cliente_cidade ?? null,
            estado: $mercosOrder->cliente_estado ?? null,
            email: implode(',', $mercosOrder->cliente_email)
        );

        $this->mapPhones($mercosOrder, $erpFlexCustomerObject);

        $response = $this->erpFlexCustomerService->create($erpFlexCustomerObject);

        Customer::updateOrCreate([
            'erp_flex_id' => $response->erpFlexId,
            'mercos_id' => $mercosOrder->cliente_id,
        ]);
    }

    /**
     * Update a single customer.
     *
     * @param  \App\Models\Customer $customer
     * @param  mixed $mercosOrder
     * @return void
     */
    protected function updateSingleCustomer(Customer $customer, mixed $mercosOrder): void
    {
        $erpFlexCustomerObject = new ErpFlexCustomer(
            id: $customer->erp_flex_id,
            nome: $mercosOrder->cliente_razao_social ?? null,
            fantasia: $mercosOrder->cliente_nome_fantasia ?? null,
            cpf_cnpj: $mercosOrder->cliente_cnpj ?? null,
            inscricao_estadual: $mercosOrder->cliente_inscricao_estadual ?? null,
            endereco: $mercosOrder->cliente_rua ?? null,
            numero: $mercosOrder->cliente_numero ?? null,
            complemento: $mercosOrder->cliente_complemento ?? null,
            cep: $mercosOrder->cliente_cep ?? null,
            bairro: $mercosOrder->cliente_bairro ?? null,
            municipio: $mercosOrder->cliente_cidade ?? null,
            estado: $mercosOrder->cliente_estado ?? null,
            email: implode(',', $mercosOrder->cliente_email)
        );

        $this->mapPhones($mercosOrder, $erpFlexCustomerObject);

        $this->erpFlexCustomerService->update($customer->erp_flex_id, $erpFlexCustomerObject);
    }

    /**
     * Map the phones to the ERPFlex structure.
     *
     * @param  mixed $mercosApiCustomer
     * @param  \App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexCustomer $erpFlexCustomerObject
     * @return void
     */
    protected function mapPhones(mixed $mercosApiCustomer, ErpFlexCustomer $erpFlexCustomerObject): void
    {
        if (empty($mercosApiCustomer->cliente_telefone)) {
            return;
        }

        foreach ($mercosApiCustomer->cliente_telefone as $mercosPhone) {
            $phone = PhoneParser::parsePhoneNumber($mercosPhone);

            if (strlen($phone['number']) === 8) {
                $erpFlexCustomerObject->ddd_residencial = $phone['code_area'];
                $erpFlexCustomerObject->telefone_residencial = $phone['number'];
            }

            if (strlen($phone['number']) === 9) {
                $erpFlexCustomerObject->ddd_celular = $phone['code_area'];
                $erpFlexCustomerObject->telefone_celular = $phone['number'];
            }
        }
    }
}
