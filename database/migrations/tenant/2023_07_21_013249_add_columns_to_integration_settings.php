<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('integration_settings', function (Blueprint $table) {
            $table->addColumn('boolean', 'enables_payment_conditions_automation')->default(false);
            $table->addColumn('boolean', 'enables_salesmen_automation')->default(false);
            $table->addColumn('boolean', 'enables_hauliers_automation')->default(false);
            $table->dateTime('last_salesman_update')->nullable();
            $table->dateTime('last_haulier_update')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('integration_settings', function (Blueprint $table) {
            $table->dropColumn('enables_payment_conditions_automation');
            $table->dropColumn('enables_salesmen_automation');
            $table->dropColumn('enables_hauliers_automation');
            $table->dropColumn('last_salesman_update');
            $table->dropColumn('last_haulier_update');
        });
    }
};
