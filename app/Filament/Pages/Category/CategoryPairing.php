<?php

namespace App\Filament\Pages\Category;

use App\Actions\Mercos\CreateCategoriesFromErpFlex;
use App\Actions\Mercos\CreateMercosCategoriesFromMercos;
use App\Actions\Pairing\CategoryPairing as CategoryPairingAction;
use App\Filament\Pages\Concerns\HandlesPairingImport;
use App\Filament\Resources\CategoryResource;
use App\Models\MercosCategory;
use App\Models\CategoryPairing as CategoryPairingModel;
use Awcodes\FilamentTableRepeater\Components\TableRepeater;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Collection;
use Throwable;

class CategoryPairing extends ListRecords
{
    use InteractsWithForms;
    use HandlesPairingImport;

    protected static ?string $title = 'Mapeamento de Categorias';
    protected static string $view = 'filament.pages.categories.category-pairing';
    protected static string $resource = CategoryResource::class;

    protected string $routeName = 'categories.pairing';
    protected bool $mercosOriginFlow = false;

    /**
     * @var \Illuminate\Support\Collection
     */
    public $erpFlexCategoriesCollection;

    public array $erpFlexCategories;

    /**
     * @var \Illuminate\Support\Collection
     */
    public $mercosCategories;

    /**
     * @var \Illuminate\Support\Collection
     */
    public Collection $erpFlexOptions;

    /**
     * @var \Illuminate\Support\Collection
     */
    public  Collection $mercosOptions;

    /** @inheritDoc */
    public function mount(): void
    {
        $this->setCategoryData();
    }

    /** @inheritDoc */
    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return false;
    }

    /**
     * Sets the categories pairing data
     *
     * @return void
     */
    protected function setCategoryData()
    {
        $this->erpFlexCategoriesCollection = CategoryPairingModel::get();
        $this->erpFlexCategories = $this->erpFlexCategoriesCollection->toArray();
        $this->mercosCategories = MercosCategory::get();

        $this->erpFlexOptions = $this->erpFlexCategoriesCollection->mapWithKeys(function ($item) {
            return [
                $item->erp_flex_id => !empty($item->erp_flex_data['SBA_Desc'])
                    ? $item->erp_flex_data['SBA_Desc']
                    : 'Sem nome'
            ];
        });

        $this->mercosOptions = $this->mercosCategories->mapWithKeys(function ($item) {
            return [
                $item->mercos_id => !empty($item->mercos_data['nome'])
                    ? $item->mercos_data['nome']
                    : 'Sem nome'
            ];
        });
    }

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            Action::make('Aprovar mapeamento')
                ->action(function (): void {
                    try {
                        $this->onApproveMapping();
                        success_notification('O mapeamento foi realizado com sucesso')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-check')
                ->color('warning')
                ->requiresConfirmation(),
            $this->defineImportAction($this->erpFlexCategoriesCollection, $this->mercosCategories, new CategoryPairingModel()),
            Action::make('Salvar rascunho')
                ->action(function (): void {
                    try {
                        $this->onSaveMapping();
                        success_notification('O rascunho foi salvo com sucesso')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->color('success')
                ->icon('heroicon-s-save'),
            Action::make('Sincronizar dados')
                ->action(function (): void {
                    try {
                        $this->onRefreshData();
                        success_notification('Os dados foram atualizados')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->color('success')
                ->icon('heroicon-s-arrow-path')
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            Section::make('Mapear categorias')
                ->compact()
                ->schema([
                    Grid::make(1)->schema([
                        TableRepeater::make('erpFlexCategories')
                            ->disableItemCreation()
                            ->disableItemMovement()
                            ->disableItemDeletion()
                            ->disableLabel()
                            ->label('')
                            ->columns(2)
                            ->schema([
                                Select::make('erp_flex_id')
                                    ->disabled()
                                    ->disableLabel()
                                    ->label('Categoria ErpFlex (um ID por linha)')
                                    ->options($this->erpFlexOptions),
                                Select::make('mercos_id')
                                    ->disableLabel()
                                    ->label('Categoria Mercos (um ID por linha)')
                                    ->options($this->mercosOptions)
                                    ->searchable()
                            ])
                    ])
                ]),
        ];
    }

    /**
     * Saves the current mapping collection
     *
     * @return void
     */
    public function onSaveMapping(): void
    {
        foreach ($this->erpFlexCategories as $category) {
            $mercosData = null;

            if (!empty($category['mercos_id'])) {
                    $mercosData = $this->mercosCategories
                    ->where('mercos_id', $category['mercos_id'])
                    ->first()?->mercos_data;
            }

            CategoryPairingModel::updateOrCreate(
                ['id' => $category['id']],
                [
                    'mercos_id' => $category['mercos_id'] ?? null,
                    'mercos_data' => $mercosData
                ]
            );
        }
    }

    /**
     * Transforms the current mapping into the proper models.
     *
     * @return mixed
     */
    public function onApproveMapping(): mixed
    {
        $this->onSaveMapping();
        CategoryPairingAction::run();

        return redirect()->to('/app/categories');
    }

    /**
     * Refreshes the data on both sides.
     *
     * @return mixed
     */
    public function onRefreshData()
    {
        CreateCategoriesFromErpFlex::run(true);
        CreateMercosCategoriesFromMercos::run(true);

        return redirect()->to(route('categories.pairing'));
    }
}
