<?php

namespace App\Http\Integrations\Mercos\Requests\Order;

use App\Http\Integrations\Mercos\Requests\MercosBaseGetRequest;

class MercosOrderGetOrdersRequest extends MercosBaseGetRequest
{
    /**
     * Create a new instance.
     *
     * @param  string|null $filters
     */
    public function __construct(protected ?string $filters = null)
    {
        $this->filters = !is_null($filters)
            ? ('?' . $this->filters)
            : '';
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v2/pedidos/' . $this->filters;
    }
}
