<?php

namespace App\Actions\Pairing;

use App\Http\Integrations\Mercos\Services\MercosProductCategoryService;
use App\Models\Category;
use App\Models\CategoryPairing as CategoryPairingModel;
use App\Models\IntegrationSetting;
use Exception;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CategoryPairing
{
    use AsAction;

    protected MercosProductCategoryService $mercosCategoryService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(tenant('id') . '_cadastros');
    }

    /**
     * Handle the action.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            if (integration_settings()->categories_pairing_finished) {
                throw new Exception('categories_pairing.pairing_should_only_be_done_once');
                return;
            }

            $pairingCollection = CategoryPairingModel::get();

            $pairingCollection->each(function (CategoryPairingModel $pairing): void {
                if (!$pairing->mercos_id) {
                    return;
                }

                Category::create([
                    'erp_flex_id' => $pairing->erp_flex_id,
                    'erp_flex_data' => $pairing->erp_flex_data,
                    'mercos_id' => $pairing->mercos_id,
                    'mercos_data' => $pairing->mercos_data,
                ]);
            });

            $integrationSetting = IntegrationSetting::first();
            $integrationSetting->categories_pairing_finished = true;
            $integrationSetting->save();
        } catch (Throwable $th) {
            error($th);
        }
    }
}
