<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Integration setting model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  bool $enables_customer_automation
 * @property  bool $enables_customer_mercos_to_erp_flex_flow
 * @property  bool $enables_products_automation
 * @property  bool $uses_category_for_mercos_products
 * @property  bool $enables_price_table_automation
 * @property  bool $enables_stock_automation
 * @property  bool $uses_reserved_quantity_in_stock_calculations
 * @property  bool $uses_only_default_warehouse_in_stock_calculations
 * @property  bool $enables_orders_automation
 * @property  bool $enables_order_billings_automation
 * @property  bool $enables_payment_conditions_automation
 * @property  bool $enables_salesmen_automation
 * @property  bool $enables_hauliers_automation
 * @property  bool $customers_pairing_finished
 * @property  bool $categories_pairing_finished
 * @property  bool $subcategories_pairing_finished
 * @property  bool $products_pairing_finished
 * @property  bool $payment_conditions_pairing_finished
 * @property  bool $hauliers_pairing_finished
 * @property  \Carbon\Carbon $last_customers_update
 * @property  \Carbon\Carbon $last_mercos_customers_update
 * @property  \Carbon\Carbon $last_categories_update
 * @property  \Carbon\Carbon $last_products_update
 * @property  \Carbon\Carbon $last_orders_update
 * @property  \Carbon\Carbon $last_erp_flex_orders_update
 * @property  \Carbon\Carbon $erp_flex_order_billings_cutoff_date
 * @property  \Carbon\Carbon $last_payment_condition_update
 * @property  \Carbon\Carbon $last_salesman_update
 * @property  \Carbon\Carbon $last_haulier_update
 * @property  \Carbon\Carbon $last_price_table_update
 * @property  \Carbon\Carbon $last_price_table_products_update
 * @property  \Carbon\Carbon $last_erp_flex_stock_update
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class IntegrationSetting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'enables_customer_automation',
        'enables_customer_mercos_to_erp_flex_flow',
        'enables_products_automation',
        'uses_category_for_mercos_products',
        'enables_price_table_automation',
        'enables_stock_automation',
        'uses_reserved_quantity_in_stock_calculations',
        'uses_only_default_warehouse_in_stock_calculations',
        'enables_orders_automation',
        'enables_order_billings_automation',
        'enables_payment_conditions_automation',
        'enables_salesmen_automation',
        'enables_hauliers_automation',
        'last_customers_update',
        'last_mercos_customers_update',
        'last_categories_update',
        'last_products_update',
        'last_orders_update',
        'last_erp_flex_orders_update',
        'erp_flex_order_billings_cutoff_date',
        'last_payment_condition_update',
        'last_salesman_update',
        'last_haulier_update',
        'last_price_table_update',
        'last_price_table_products_update',
        'last_erp_flex_stock_update',
        'customers_pairing_finished',
        'categories_pairing_finished',
        'subcategories_pairing_finished',
        'products_pairing_finished',
        'payment_conditions_pairing_finished',
        'hauliers_pairing_finished',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'uses_reserved_quantity_in_stock_calculations' => 'bool',
        'uses_only_default_warehouse_in_stock_calculations' => 'bool',
        'uses_category_for_mercos_products' => 'bool',
    ];
}
