<?php

namespace App\Http\Integrations\Mercos\Requests\Order;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderStatus;
use App\Http\Integrations\Mercos\Requests\MercosBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosCreateOrderBillingRequest extends MercosBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderStatus $mercosOrderStatus
     */
    public function __construct(protected MercosOrderStatus $mercosOrderStatus)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/faturamento';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return array_filter([
            'pedido_id' => $this->mercosOrderStatus->pedido_id,
            'valor_faturado' => $this->mercosOrderStatus->valor_faturado,
            'data_faturamento' => $this->mercosOrderStatus->data_faturamento,
            'numero_nf' => $this->mercosOrderStatus->numero_nf,
            'informacoes_adicionais' => $this->mercosOrderStatus->informacoes_adicionais,
        ]);
    }
}
