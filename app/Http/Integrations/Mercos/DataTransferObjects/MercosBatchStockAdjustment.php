<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosBatchStockAdjustment
{
    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosStockAdjustment[] $produto_id
     */
    public function __construct(
        public array $stockAdjustments
    ) {
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return collect($this->stockAdjustments)
            ->map(fn (MercosStockAdjustment $mercosStockAdjustment): array => $mercosStockAdjustment->toArray())
            ->toArray();
    }
}
