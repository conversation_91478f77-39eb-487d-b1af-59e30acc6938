<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosProduct
{
    /**
     * Create a new instance.
     *
     * @param  string|null $codigo
     * @param  string|null $nome
     * @param  int|null $id
     * @param  float|null $comissao
     * @param  float|null $preco_tabela
     * @param  float|null $preco_minimo
     * @param  float|null $ipi
     * @param  string|null $tipo_ipi
     * @param  float|null $st
     * @param  array|null $grade_cores
     * @param  array|null $grade_tamanhos
     * @param  string|null $moeda
     * @param  string|null $unidade
     * @param  float|null $saldo_estoque
     * @param  string|null $observacoes
     * @param  string|null $ultima_alteracao
     * @param  bool|null $excluido
     * @param  bool|null $ativo
     * @param  int|null $categoria_id
     * @param  string|null $codigo_ncm
     * @param  float|null $multiplo
     * @param  float|null $peso_bruto
     * @param  float|null $largura
     * @param  float|null $altura
     * @param  float|null $comprimento
     * @param  bool|null $peso_dimensoes_unitario
     * @param  bool|null $exibir_no_b2b
     */
    public function __construct(
        public ?string $codigo,
        public ?string $nome,
        public ?int $id = null,
        public ?float $comissao = null,
        public ?float $preco_tabela = 0.00,
        public ?float $preco_minimo = 0.00,
        public ?float $ipi = null,
        public ?string $tipo_ipi = 'P',
        public ?float $st = null,
        public ?array $grade_cores = null,
        public ?array $grade_tamanhos = null,
        public ?string $moeda = '0',
        public ?string $unidade = null,
        public ?float $saldo_estoque = 0,
        public ?string $observacoes = null,
        public ?string $ultima_alteracao = null,
        public ?bool $excluido = null,
        public ?bool $ativo = true,
        public ?int $categoria_id = null,
        public ?string $codigo_ncm = null,
        public ?float $multiplo = null,
        public ?float $peso_bruto = null,
        public ?float $largura = null,
        public ?float $altura = null,
        public ?float $comprimento = null,
        public ?bool $peso_dimensoes_unitario = null,
        public ?bool $exibir_no_b2b = null
    ) {
    }

    /**
     * Create a new instance from an object.
     *
     * @param  object $object
     * @return static
     */
    public static function fromObject(object $object): static
    {
        return new static(
            id: $object->id,
            codigo: $object->codigo,
            nome: $object->nome,
            comissao: $object->comissao,
            preco_tabela: $object->preco_tabela,
            preco_minimo: $object->preco_minimo,
            ipi: $object->ipi,
            tipo_ipi: $object->tipo_ipi,
            st: $object->st,
            grade_cores: $object->grade_cores,
            grade_tamanhos: $object->grade_tamanhos,
            moeda: $object->moeda,
            unidade: $object->unidade,
            saldo_estoque: $object->saldo_estoque,
            observacoes: $object->observacoes,
            ultima_alteracao: $object->ultima_alteracao,
            excluido: $object->excluido,
            ativo: $object->ativo,
            categoria_id: $object->categoria_id,
            codigo_ncm: $object->codigo_ncm,
            multiplo: $object->multiplo,
            peso_bruto: $object->peso_bruto,
            largura: $object->largura,
            altura: $object->altura,
            comprimento: $object->comprimento,
            peso_dimensoes_unitario: $object->peso_dimensoes_unitario,
            exibir_no_b2b: $object->exibir_no_b2b
        );
    }
}
