<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosSalesman;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosSalesmanResponse;
use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\Salesman\MercosSalesmanCreateSalesmanRequest;
use App\Http\Integrations\Mercos\Requests\Salesman\MercosSalesmanDeleteSalesmanRequest;
use App\Http\Integrations\Mercos\Requests\Salesman\MercosSalesmanGetSalesmanRequest;
use App\Http\Integrations\Mercos\Requests\Salesman\MercosSalesmanGetSalesmenRequest;
use App\Http\Integrations\Mercos\Requests\Salesman\MercosSalesmanUpdateSalesmanRequest;
use App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse;
use App\Http\Integrations\Mercos\Services\Interfaces\Getable;

class MercosSalesmanService extends MercosBaseService implements Getable
{
    use HandlesLogCreation;

    /**
     * Get all customers.
     *
     * @param  string|null $filters
     * @return \App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse
     */
    public function get(?string $filters = null): MercosGetCollectionDefaultResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosSalesmanGetSalesmenRequest($filters))
            ->send();

        $this->createLog($response);

        $body = json_decode($response->body());

        if ($response->status() === 429) {
            $delayForRateLimiting = (int) $body->tempo_ate_permitir_novamente + 1;
        }

        return new MercosGetCollectionDefaultResponse(
            is_array($body) ? $body : [],
            $delayForRateLimiting,
            (int) $response->header('MEUSPEDIDOS_REQUISICOES_EXTRAS'),
            $response->successful()
        );
    }

    /**
     * Get a customer by its ID.
     *
     * @param  int $id
     * @return mixed
     */
    public function getById(int $id): mixed
    {
        return json_decode(
            (new MercosConnector($this->applicationToken, $this->companyToken))
                ->request(new MercosSalesmanGetSalesmanRequest($id))
                ->send()
                ->body()
        );
    }

    /**
     * Create a customer.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosSalesman $mercosSalesman
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosSalesmanResponse
     */
    public function create(MercosSalesman $mercosSalesman): MercosSalesmanResponse
    {
        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosSalesmanCreateSalesmanRequest($mercosSalesman))
            ->send();

        return new MercosSalesmanResponse(mercosId: $response->header('MeusPedidosID'));
    }

    /**
     * Update a customer.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosSalesman $mercosSalesman
     * @return mixed
     */
    public function update(int $id, MercosSalesman $mercosSalesman): mixed
    {
        return (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosSalesmanUpdateSalesmanRequest($id, $mercosSalesman))
            ->send()
            ->ok();
    }

    /**
     * Delete a customer.
     *
     * @param  int $id
     * @return mixed
     */
    public function delete(int $id): mixed
    {
        return (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosSalesmanDeleteSalesmanRequest($id))
            ->send()
            ->ok();
    }
}
