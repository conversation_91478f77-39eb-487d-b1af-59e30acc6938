<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Order model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $erp_flex_id
 * @property  string $mercos_id
 * @property  string $invoice_id
 * @property  boolean $receivables_sent
 * @property  boolean $invoices_sent
 * @property  array $erp_flex_data
 * @property  array $mercos_data
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \Illuminate\Support\Collection|\App\Models\OrderBilling[] $orderBillings
 */
class Order extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'erp_flex_id',
        'mercos_id',
        'invoice_id',
        'receivables_sent',
        'invoices_sent',
        'erp_flex_data',
        'mercos_data'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'erp_flex_data' => 'array',
        'mercos_data' => 'array'
    ];

    /**
     * Load the order billings relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function orderBillings(): HasMany
    {
        return $this->hasMany(OrderBilling::class);
    }
}
