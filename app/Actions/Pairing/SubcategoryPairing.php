<?php

namespace App\Actions\Pairing;

use App\Http\Integrations\Mercos\Services\MercosProductCategoryService;
use App\Models\Category;
use App\Models\IntegrationSetting;
use App\Models\Subcategory;
use App\Models\SubcategoryPairing as SubcategoryPairingModel;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class SubcategoryPairing
{
    use AsAction;

    protected MercosProductCategoryService $mercosCategoryService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(tenant('id') . '_cadastros');
    }

    /**
     * Handle the action.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            if (integration_settings()->subcategories_pairing_finished) {
                throw new Exception('subcategories_pairing.pairing_should_only_be_done_once');
                return;
            }

            $pairingCollection = SubcategoryPairingModel::get();

            $pairingCollection->each(function (SubcategoryPairingModel $pairing): void {
                if (!$pairing->mercos_id) {
                    return;
                }

                try {
                    $parentCategory = Category::query()
                        ->where('erp_flex_id', $pairing->erp_flex_data['SBA_IDPAI'])
                        ->first();

                    if (is_null($parentCategory)) {
                        info('Parent category not found on pairing ID: ' . $pairing->id);
                        throw new Exception('subcategories_pairing.parent_category_not_found');
                    }

                    Subcategory::create([
                        'category_id' => $parentCategory->id,
                        'erp_flex_id' => $pairing->erp_flex_id,
                        'erp_flex_data' => $pairing->erp_flex_data,
                        'mercos_id' => $pairing->mercos_id,
                        'mercos_data' => $pairing->mercos_data,
                    ]);
                } catch (Throwable $th) {
                    // @todo - Evoluir essa trativa.
                    // Caso o ID da categoria PAI não esteja mapeado, vamos ter um erro no banco
                    // de integridade com o ID da categoria.
                    // Precisamos criar um tipo de visualização, para que o cliente consiga ver
                    // as subcategorias com problemas de integridade.
                    // Neste momento está apenas ignorando.
                    error($th);
                }
            });

            $integrationSetting = IntegrationSetting::first();
            $integrationSetting->subcategories_pairing_finished = true;
            $integrationSetting->save();
        } catch (Throwable $th) {
            error($th);
        }
    }
}
