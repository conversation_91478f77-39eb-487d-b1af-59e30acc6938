<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\ErpFlex\ErpFlexConnector;
use App\Http\Integrations\ErpFlex\Requests\Subcategory\ErpFlexSubcategoryGetSubcategoriesRequest;

class ErpFlexSubcategoryService extends ErpFlexBaseService
{
    /**
     * Get all subcategories.
     *
     * @param  int $page
     * @return mixed
     */
    public function get(int $page = 1): mixed
    {
        return json_decode(
            (new ErpFlexConnector($this->username, $this->password))
                ->request(new ErpFlexSubcategoryGetSubcategoriesRequest($page))
                ->send()
        );
    }
}
