<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CustomerResource\Concerns\HandlesCustomerResourceForm;
use App\Filament\Resources\CustomerResource\Concerns\HandlesCustomerResourceTable;
use App\Filament\Resources\CustomerResource\Pages;
use App\Filament\Resources\CustomerResource\Pages\ManageCustomers;
use App\Filament\Resources\CustomerResource\RelationManagers;
use App\Models\Customer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CustomerResource extends Resource
{
    use HandlesCustomerResourceForm;
    use HandlesCustomerResourceTable;

    protected static ?string $model = Customer::class;
    protected static ?string $modelLabel = 'cliente';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Configure the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => ManageCustomers::route('/'),
        ];
    }
}
