<?php

namespace App\Http\Integrations\ErpFlex\Requests\Category;

use App\Http\Integrations\ErpFlex\Requests\ErpFlexBaseGetRequest;

class ErpFlexCategoryGetCategoriesRequest extends ErpFlexBaseGetRequest
{
    /**
     * Create a new instance.
     *
     * @param  int $page
     */
    public function __construct(protected int $page = 1)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/api_v2/categoria/pagina/' . $this->page;
    }
}
