<?php

namespace App\Actions\Mercos;

use App\Actions\Mercos\Concerns\HandlesMercosDefaultApiCollectionGetRequests;
use App\Http\Integrations\Mercos\Services\MercosPaymentConditionService;
use App\Models\IntegrationSetting;
use App\Models\MercosPaymentCondition;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateMercosPaymentConditionsFromMercos
{
    use AsAction;
    use HandlesMercosDefaultApiCollectionGetRequests;

    protected bool $force = false;

    protected MercosPaymentConditionService $mercosPaymentConditionService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(tenant('id') . '_cadastros');
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_payment_conditions_automation) {
            return;
        }

        $this->force = $force;

        $this->mercosPaymentConditionService = new MercosPaymentConditionService();

        $this->integratePaymentConditions();
    }

    /**
     * Integrate all payment conditions Mercos for pairing.
     *
     * @return void
     */
    protected function integratePaymentConditions(): void
    {
        try {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();
            $executionStart = now();

            $mercosApiPaymentConditions = $this->getPaymentConditionsCollection();

            foreach ($mercosApiPaymentConditions as $mercosApiPaymentCondition) {
                $this->processSinglePaymentCondition($mercosApiPaymentCondition);
            }

            $integrationSetting->update(['last_payment_condition_update' => $executionStart]);
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Get the payment conditions collection for a given offset page.
     *
     * @return \Illuminate\Support\Collection
     */
    protected function getPaymentConditionsCollection(): Collection
    {
        $lastUpdatedAt = Carbon::parse(integration_settings()->last_payment_condition_update)
            ->setTimezone('America/Sao_Paulo')->format('Y-m-d H:i:s');

        return $this->getMercosApiCollection($this->mercosPaymentConditionService, $lastUpdatedAt);
    }

    /**
     * Process a single payment condition.
     *
     * @param  mixed $mercosApiPaymentCondition
     * @return void
     */
    protected function processSinglePaymentCondition(mixed $mercosApiPaymentCondition): void
    {
        /** @var \App\Models\PaymentCondition $paymentCondition */
        $paymentCondition = MercosPaymentCondition::updateOrCreate([
            'mercos_id' => $mercosApiPaymentCondition->id
        ], [
            'mercos_data' => json_decode(json_encode($mercosApiPaymentCondition), true),
        ]);
    }
}
