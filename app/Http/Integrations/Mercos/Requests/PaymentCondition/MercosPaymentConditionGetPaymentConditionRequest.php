<?php

namespace App\Http\Integrations\Mercos\Requests\PaymentCondition;

use App\Http\Integrations\Mercos\Requests\MercosBaseGetRequest;

class MercosPaymentConditionGetPaymentConditionRequest extends MercosBaseGetRequest
{
    /**
     * Create a new instance.
     *
     * @param  int $id
     */
    public function __construct(protected int $id)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/condicoes_pagamento/' . $this->id;
    }
}
