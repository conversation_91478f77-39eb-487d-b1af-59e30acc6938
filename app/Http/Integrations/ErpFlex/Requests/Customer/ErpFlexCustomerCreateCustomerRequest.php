<?php

namespace App\Http\Integrations\ErpFlex\Requests\Customer;

use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexCustomer;
use App\Http\Integrations\ErpFlex\Requests\ErpFlexBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class ErpFlexCustomerCreateCustomerRequest extends ErpFlexBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexCustomer $erpFlexCustomer
     */
    public function __construct(
        protected ErpFlexCustomer $erpFlexCustomer
    ) {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/api/cliente';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        $data = [];

        foreach ($this->erpFlexCustomer as $property => $value) {
            // Only return the property if the value is not null.
            // This is necessary to avoid errors on the ErpFlex API.
            // The API doesn't ignore null values.
            if (!is_null($value)) {
                $data[$property] = $value;
            }
        }

        return $data;
    }
}
