<?php

namespace App\Actions;

use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class DefineNextMercosJobQueue
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @return string
     */
    public function handle(): string
    {
        $queue = DB::connection(config('tenancy.database.central_connection'))
            ->table('predefined_mercos_queues')
            ->leftJoin('jobs', 'jobs.queue', 'predefined_mercos_queues.queue')
            ->select([
                'predefined_mercos_queues.queue',
                DB::raw('count(1) as queue_count'),
            ])
            ->where('predefined_mercos_queues.queue', 'like', "mercos_%")
            ->groupBy('predefined_mercos_queues.queue')
            ->orderBy('queue_count')
            ->first();

        return !is_null($queue)
            ? $queue->queue
            : 'mercos_1';
    }
}
