<?php

namespace App\Models\ErpFlex;

use Illuminate\Database\Eloquent\Relations\HasOne;

 /**
 * SBP (Price Table) Model.
 *
 * @package App\Models\ErpFlex;
 *
 * @property  int $SBP_Tabela
 * @property  int $SBP_IDSB2
 * @property  int $SBP_Preco
 */
class SBP extends BaseModel
{
    protected $table = 'SBP';
    protected $primaryKey = 'SBP_ID';
    protected $prefix = 'SBP';

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'SBP_DT_INC' => 'datetime',
        'SBP_DT_ALT' => 'datetime',
    ];

    /**
     * SB2 has one relationship.
     *
     * @return HasOne
     */
    public function sb2(): HasOne
    {
        return $this->hasOne(SB2::class, 'SB2_ID', 'SBP_IDSB2');
    }
}
