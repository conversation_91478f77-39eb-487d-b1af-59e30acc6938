<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosCustomer;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosCustomerResponse;
use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\Customer\MercosCustomerCreateCustomerRequest;
use App\Http\Integrations\Mercos\Requests\Customer\MercosCustomerDeleteCustomerRequest;
use App\Http\Integrations\Mercos\Requests\Customer\MercosCustomerGetCustomerRequest;
use App\Http\Integrations\Mercos\Requests\Customer\MercosCustomerGetCustomersRequest;
use App\Http\Integrations\Mercos\Requests\Customer\MercosCustomerUpdateCustomerRequest;
use App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse;
use App\Http\Integrations\Mercos\Services\Interfaces\Getable;

class MercosCustomerService extends MercosBaseService implements Getable
{
    use HandlesLogCreation;

    /**
     * Get all customers.
     *
     * @param  string|null $filters
     * @return \App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse
     */
    public function get(?string $filters = null): MercosGetCollectionDefaultResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosCustomerGetCustomersRequest($filters))
            ->send();

        $this->createLog($response);

        $body = json_decode($response->body());

        if ($response->status() === 429) {
            $delayForRateLimiting = (int) $body->tempo_ate_permitir_novamente + 1;
        }

        return new MercosGetCollectionDefaultResponse(
            is_array($body) ? $body : [],
            $delayForRateLimiting,
            (int) $response->header('MEUSPEDIDOS_REQUISICOES_EXTRAS'),
            $response->successful()
        );
    }

    /**
     * Get a customer by its ID.
     *
     * @param  int $id
     * @return mixed
     */
    public function getById(int $id): mixed
    {
        return json_decode(
            (new MercosConnector($this->applicationToken, $this->companyToken))
                ->request(new MercosCustomerGetCustomerRequest($id))
                ->send()
                ->body()
        );
    }

    /**
     * Create a customer.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosCustomer $mercosCustomer
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosCustomerResponse
     */
    public function create(MercosCustomer $mercosCustomer): MercosCustomerResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $request = new MercosCustomerCreateCustomerRequest($mercosCustomer);

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request($request)
            ->send();

        $body = json_decode($response->body());

        $success = !isset($body->erros) || count($body->erros) === 0;

        $this->createLog(
            response: $response,
            success: $success,
            errorDescription: $success
                ? null
                : $body->mensagem,
            requestBody: $request->defaultData(),
        );

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosCustomerResponse(
            mercosId: !empty($response->header('MeusPedidosID'))
                ? $response->header('MeusPedidosID')
                : null,
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 201
        );
    }

    /**
     * Update a customer.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosCustomer $mercosCustomer
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosCustomerResponse
     */
    public function update(int $id, MercosCustomer $mercosCustomer): MercosCustomerResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosCustomerUpdateCustomerRequest($id, $mercosCustomer))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosCustomerResponse(
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 200
        );
    }

    /**
     * Delete a customer.
     *
     * @param  int $id
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosCustomerResponse
     */
    public function delete(int $id): MercosCustomerResponse
    {
        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosCustomerDeleteCustomerRequest($id))
            ->send();

        return new MercosCustomerResponse(
            delayForRateLimiting: $response->body() !== ''
                ? json_decode($response->body())->tempo_ate_permitir_novamente
                : null
        );
    }
}
