<?php

namespace App\Http\Integrations\ErpFlex\DataTransferObjects;

class ErpFlexOrder
{
    /**
     * Create a new instance.
     *
     * @param  string $documento
     * @param  string $emissao
     * @param  string $cliente_id
     * @param  string $vendedor_id
     * @param  string|null $transportadora
     * @param  string $historico
     * @param  string $itens
     * @param  string|null $campo1
     * @param  string|null $campo2
     * @param  string|null $id
     */
    public function __construct(
        public string $documento,
        public string $emissao,
        public string $cliente_id,
        public string $vendedor_id,
        public ?string $transportadora,
        public string $historico,
        public int $formas_pags_id,
        public ?float $valor_frete,
        public string $itens,
        public ?int $tabela_preco = null,
        public ?string $campo1 = null,
        public ?string $campo2 = null,
        public ?string $id = null,
    ) {
    }

    /**
     * @inheritDoc
     */
    public function toArray(bool $create = false): array
    {
        $data = array_filter([
            'documento' => $this->documento,
            'emissao' => $this->emissao,
            'cliente_id' => $this->cliente_id,
            'vendedor_id' => $this->vendedor_id,
            'transportadora' => $this->transportadora,
            'historico' => $this->historico,
            'formas_pags_id' => $this->formas_pags_id,
            'valor_frete' => $this->valor_frete,
            'tabela_preco' => $this->tabela_preco,
            'itens' => $this->itens,
            'campo1' => $this->campo1,
            'campo2' => $this->campo2,
            'id' => $this->id,
        ]);

        if ($create) {
            unset($data['id']);
        }

        return $data;
    }
}
