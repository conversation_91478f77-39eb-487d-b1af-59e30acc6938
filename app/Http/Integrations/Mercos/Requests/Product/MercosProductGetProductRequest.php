<?php

namespace App\Http\Integrations\Mercos\Requests\Product;

use App\Http\Integrations\Mercos\Requests\MercosBaseGetRequest;

class MercosProductGetProductRequest extends MercosBaseGetRequest
{
    /**
     * Create a new instance.
     *
     * @param  int $id
     */
    public function __construct(protected int $id)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/produtos/' . $this->id;
    }
}
