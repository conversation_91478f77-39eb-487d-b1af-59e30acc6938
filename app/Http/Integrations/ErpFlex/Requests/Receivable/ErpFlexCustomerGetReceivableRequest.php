<?php

namespace App\Http\Integrations\ErpFlex\Requests\Receivable;

use App\Http\Integrations\ErpFlex\Requests\ErpFlexBaseGetRequest;

class ErpFlexCustomerGetReceivableRequest extends ErpFlexBaseGetRequest
{
    /**
     * Create a new instance.
     *
     * @param  int $id
     */
    public function __construct(
        protected int $id,
        protected int $banco,
        protected int $carteira
    ) {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return "/api_v2/boleto?idRegistro=$this->id&idBanco=$this->banco&idCarteira=$this->carteira&tipo=pdf";
    }
}
