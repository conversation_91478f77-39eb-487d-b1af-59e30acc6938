<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('integration_settings', function (Blueprint $table) {
            $table->boolean('enables_customer_automation')->after('id')->default(false);
            $table->boolean('enables_products_automation')->after('enables_customer_automation')->default(false);
            $table->boolean('enables_price_table_automation')->after('enables_products_automation')->default(false);
            $table->boolean('enables_stock_automation')->after('enables_price_table_automation')->default(false);
            $table->boolean('enables_orders_automation')->after('enables_stock_automation')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('integration_settings', function (Blueprint $table) {
            $table->dropColumn('enables_customer_automation');
            $table->dropColumn('enables_products_automation');
            $table->dropColumn('enables_price_table_automation');
            $table->dropColumn('enables_stock_automation');
            $table->dropColumn('enables_orders_automation');
        });
    }
};
