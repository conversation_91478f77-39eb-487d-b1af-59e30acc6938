<?php

namespace App\Filament\Resources\OrderResource\Concerns;

use App\Actions\ErpFlex\CreateOrdersFromMercos;
use App\Models\Order;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables;
use Throwable;

trait HandlesOrderResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')->label('ID'),
            TextColumn::make('erp_flex_id')->label('ID ERPFlex'),
            TextColumn::make('mercos_id')->label('ID Mercos'),
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [
            Tables\Actions\ActionGroup::make([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('reprocess')
                    ->label('Reprocessar')
                    ->visible(fn (Order $order): bool => is_null($order->erp_flex_id))
                    ->requiresConfirmation()
                    ->action(function (Order $order) {
                        try {
                            CreateOrdersFromMercos::run(true, $order->mercos_id, true);
                            success_notification('O pedido foi integrado.')->send();
                            return redirect()->route('filament.app.resources.orders.index');
                        } catch (Throwable $th) {
                            error_notification('Não foi possível integrar o pedido. Tente novamente mais tarde.')->send();
                        }
                    }),
                Tables\Actions\Action::make('unlink')
                    ->label('Desvincular do ERPFlex')
                    ->visible(fn (Order $order): bool => !is_null($order->erp_flex_id))
                    ->requiresConfirmation()
                    ->action(function (Order $order) {
                        try {
                            $order->update(['erp_flex_id' => null]);
                            success_notification('O ID do pedido do ERPFlex foi desvinculado desta plataforma. Agora, você já pode reprocessá-lo pelo mesmo menu. Não se esqueça de excluir o pedido no ERPFlex também.')->send();
                            return redirect()->route('filament.app.resources.orders.index');
                        } catch (Throwable $th) {
                            error_notification('Não foi possível desvinculado o ID do pedido. Tente novamente mais tarde.')->send();
                        }
                    })
            ]),
        ];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [];
    }
}
