<?php

namespace App\Http\Integrations\Mercos\Requests\CustomerXSalesman;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosCustomerXSalesman;
use App\Http\Integrations\Mercos\Requests\MercosBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosCustomerCreateCustomerXSalesmanRequest extends MercosBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosCustomerXSalesman $mercosCustomerXSalesman
     */
    public function __construct(protected MercosCustomerXSalesman $mercosCustomerXSalesman)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/usuarios_clientes';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return [
            'cliente_id' => $this->mercosCustomerXSalesman->cliente_id,
            'usuario_id' => $this->mercosCustomerXSalesman->usuario_id,
            'liberado' => $this->mercosCustomerXSalesman->liberado,
        ];
    }
}
