<?php

namespace App\Filament\Resources\OrderResource\Concerns;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Novadaemon\FilamentPrettyJson\PrettyJson;

trait HandlesOrderResourceForm
{
    /**
     * Configure the resource's form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Grid::make(2)->schema([
                TextInput::make('erp_flex_id')
                    ->label('ID ERPFlex'),
                TextInput::make('mercos_id')
                    ->label('ID Mercos'),
            ]),
            Grid::make(1)->schema([
                PrettyJson::make('mercos_data')
                    ->label('Dados do pedido')
                    ->formatStateUsing(fn (Get $get) => json_encode($get('mercos_data'))),
            ]),
        ];
    }
}
