<?php

namespace App\Actions;

use App\Actions\ErpFlex\CreateSalesmenFromMercos;
use App\Actions\Mercos\CreateCategoriesFromErpFlex;
use App\Actions\Mercos\CreateCustomersFromErpFlex;
use App\Actions\Mercos\CreateCustomersXSalesmenLinkFromErpFlex;
use App\Actions\Mercos\CreateHauliersFromErpFlex;
use App\Actions\Mercos\CreatePaymentConditionsFromErpFlex;
use App\Actions\Mercos\CreateProductsFromErpFlex;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class RunErpFlexManagementFlow
{
    use AsAction;

    public string $commandSignature = 'efm:run-erp-flex-management-flow {tenant?} {force?}';
    public string $commandDescription = 'Runs the ERPFlex > Mercos flow, going through payment conditions, hauliers, salesmen, customers and products.';

    /**
     * Handle the action as a command.
     *
     * @param  \Illuminate\Console\Command $command
     * @return void
     */
    public function asCommand(Command $command): void
    {
        $this->handle(
            tenant: $command->argument('tenant'),
            force: (bool) $command->argument('force')
        );
    }

    /**
     * Handle the action.
     *
     * @param  string|null $tenant
     * @param  bool $force
     * @return void
     */
    public function handle(?string $tenant = null, bool $force = false): void
    {
        $tenants = Tenant::query()
            ->when($tenant, fn (Builder $query): Builder => $query->where('id', $tenant))
            ->get();

        tenancy()->runForMultiple($tenants, function () use ($force) {
            CreatePaymentConditionsFromErpFlex::dispatch($force);
            CreateHauliersFromErpFlex::dispatch($force);
            CreateSalesmenFromMercos::dispatch();
            CreateCustomersFromErpFlex::dispatch($force, null);
            CreateCustomersXSalesmenLinkFromErpFlex::dispatch($force);
            CreateCategoriesFromErpFlex::dispatch($force);
            CreateProductsFromErpFlex::dispatch($force);
        });
    }
}
