<?php

namespace App\Models\ErpFlex;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Category and subcategories table for the ErpFlex.
 */
class SBA extends BaseModel
{
    protected $table = 'SBA';
    protected $primaryKey = 'SBA_ID';
    protected $prefix = 'SBA';

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'SB1_DT_INC' => 'datetime',
        'SB1_DT_ALT' => 'datetime',
    ];

    public function sbaPai(): BelongsTo
    {
        return $this->belongsTo(SBA::class, 'SBA_IDPAI', 'SBA_ID');
    }
}
