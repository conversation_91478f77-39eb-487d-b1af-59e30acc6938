<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * CustomerXSaleman model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $customer_id
 * @property  string $salesman_id
 * @property  string $erp_flex_customer_id
 * @property  string $erp_flex_salesman_id
 * @property  string $mercos_customer_id
 * @property  string $mercos_salesman_id
 * @property  boolean $active
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class CustomerXSaleman extends Model
{
    use HasFactory;

    protected $table = 'customer_salesman';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'salesman_id',
        'erp_flex_customer_id',
        'erp_flex_salesman_id',
        'mercos_customer_id',
        'mercos_salesman_id',
        'active',
    ];
}
