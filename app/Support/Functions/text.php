<?php

use Filament\Forms\Components\TextInput\Mask;
use Illuminate\Support\Str;

/**
 * Normalize a text with ASCII table, trimming and making it uppercase.
 *
 * @param  string|null $value
 * @return string|null
 */
function normalize_text_upper(?string $value): ?string
{
    return !is_null($value)
        ? trim(Str::ascii(mb_strtoupper($value)))
        : null;
}

/**
 * Return the numbers of a string.
 *
 * @param  string|null $value
 * @return string|null
 */
function get_numbers(?string $value): ?string
{
    return is_null($value)
        ? null
        : preg_replace('/[^0-9]+/', '', $value);
}

/**
 * Mask a string with CPF format.
 *
 * @param  string|null $value
 * @return string|null
 */
function mask_cpf(?string $value): ?string
{
    return is_null($value)
        ? null
        : substr($value, 0, 3) . '.' . substr($value, 3, 3) . '.' . substr($value, 6, 3) . '-' . substr($value, -2);
}

/**
 * Unmask a CPF string.
 *
 * @param  string|null $value
 * @return string|null
 */
function unmask_cpf(?string $value): ?string
{
    return get_numbers($value);
}

/**
 * Mask a string with CNPJ format.
 *
 * @param  string|null $value
 * @return string|null
 */
function mask_cnpj(?string $value): ?string
{
    return is_null($value)
        ? null
        : substr($value, 0, 2) . '.' . substr($value, 2, 3) . '.' . substr($value, 5, 3) . '/' . substr($value, 8, 4) . '-' . substr($value, -2);
}

/**
 * Unmask a CNPJ string.
 *
 * @param  string|null $value
 * @return string
 */
function unmask_cnpj(?string $value): string
{
    return get_numbers($value);
}

/**
 * Mask a string with CNAE format.
 *
 * @param  string|null $value
 * @return string|null
 */
function mask_cnae(?string $value): ?string
{
    return is_null($value)
        ? null
        : substr($value, 0, 4) . '-' . substr($value, 4, 1) . '/' . substr($value, 5, 2);
}

/**
 * Unmask a CNAE string.
 *
 * @param  string|null $value
 * @return string
 */
function unmask_cnae(?string $value): string
{
    return get_numbers($value);
}

/**
 * Mask a string with brazilian zipcode format.
 *
 * @param  string|null $value
 * @return string|null
 */
function mask_zipcode(?string $value): ?string
{
    return is_null($value)
        ? null
        : substr($value, 0, 5) . '-' . substr($value, 5, 3);
}

/**
 * Unmask a zipcode string.
 *
 * @param  string|null $value
 * @return string
 */
function unmask_zipcode(?string $value): string
{
    return get_numbers($value);
}

/**
 * Mask a string with BRL (brazilian reais) currency format.
 *
 * @param  float|null $value
 * @return string
 */
function mask_money(?float $value): string
{
    if (is_null($value)) {
        return '';
    }

    return 'R$ ' . number_format($value, 2, ',', '.');
}

/**
 * Unmask a BRL (brazilian reais) currency string.
 *
 * @param  string|null $value
 * @return float
 */
function unmask_money(?string $value): float
{
    if (is_null($value)) {
        return 0.0;
    }

    $value = Str::remove(['R$'], $value);

    if (is_numeric($value)) {
        return (float) $value;
    }

    return (float) str_replace(['R$', '.', ','], ['', '', '.'], $value);
}

/**
 * Mask a string with percentage format.
 *
 * @param  float|null $value
 * @return string
 */
function mask_percentage(?float $value): string
{
    if (is_null($value)) {
        return '';
    }

    return number_format($value, 2, ',', '.') . '%';
}

/**
 * Unmask a percentage string.
 *
 * @param  string|null $value
 * @return float
 */
function unmask_percentage(?string $value): float
{
    if (is_null($value)) {
        return 0.0;
    }

    if (is_numeric($value)) {
        return (float) $value;
    }

    return (float) str_replace(['%', '.', ','], ['', '', '.'], $value);
}

/**
 * Mask a phone string.
 *
 * @param  string|null $value
 * @return float
 */
function mask_phone(?string $value) {
    if (is_null($value)) {
        return null;
    }

    $value = preg_replace('/[^0-9]/', '', $value);

    if (strlen($value) == 10) {
        // Format phone number: (XX) XXXX-XXXX
        $formattedNumber = '(' . substr($value, 0, 2) . ') ' . substr($value, 2, 4) . '-' . substr($value, 6);
    } elseif (strlen($value) == 11) {
        // Format mobile number: (XX) XXXXX-XXXX
        $formattedNumber = '(' . substr($value, 0, 2) . ') ' . substr($value, 2, 5) . '-' . substr($value, 7);
    } else {
        $formattedNumber = $value;
    }

    return $formattedNumber;
}

/**
 * Unmask a phone string.
 *
 * @param  string|null $value
 * @return string|null
 */
function unmask_phone(?string $value): ?string
{
    if (is_null($value)) {
        return null;
    }

    return trim(str_replace(['(', ')', '-', ' '], ['', '', '', ''], $value));
}

function getDefaultFilamentQuantityMask(Mask $mask, int $decimalPlaces = 2): Mask
{
    return $mask
        ->numeric()
        ->minValue(1)
        ->thousandsSeparator('.')
        ->decimalPlaces($decimalPlaces)
        ->decimalSeparator(',')
        ->signed()
        ->padFractionalZeros()
        ->mapToDecimalSeparator(['.'])
        ->normalizeZeros(false);
}

function getDefaultFilamentMoneyMask(Mask $mask): Mask
{
    return $mask
        ->patternBlocks([
            'money' => function (Mask $mask) {
                return $mask
                    ->numeric()
                    ->thousandsSeparator('.')
                    ->decimalPlaces(2)
                    ->decimalSeparator(',')
                    ->signed()
                    ->padFractionalZeros()
                    ->mapToDecimalSeparator(['.'])
                    ->normalizeZeros(false);
            }
        ])
        ->pattern('R$money')
        ->lazyPlaceholder(false);
}

function getDefaultFilamentPercentageMask(Mask $mask): Mask
{
    return $mask
        ->patternBlocks([
            'percentage' => function (Mask $mask) {
                return $mask
                    ->numeric()
                    ->thousandsSeparator('.')
                    ->decimalPlaces(2)
                    ->decimalSeparator(',')
                    ->signed()
                    ->padFractionalZeros()
                    ->mapToDecimalSeparator(['.'])
                    ->normalizeZeros(false);
            }
        ])
        ->pattern('percentage%')
        ->lazyPlaceholder(false);
}

/**
 * Trims and applies a limit to a text.
 *
 * @param string|null $value
 * @param integer $limit
 * @return string|null
 */
function textLimit(?string $value, int $limit = 100): ?string
{
    if (is_null($value)) {
        return null;
    }

    return Str::limit(trim($value), $limit, '');
}
