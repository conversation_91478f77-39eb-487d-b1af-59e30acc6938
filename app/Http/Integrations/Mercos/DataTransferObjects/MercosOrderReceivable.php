<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosOrderReceivable
{
    /**
     * Create a new instance.
     *
     * @param  int $cliente_id
     * @param  string $numero_documento
     * @param  float $valor
     * @param  string $data_vencimento
     * @param  string|null $pedido_id
     * @param  string|null $arquivo_pdf
     * @param  string|null $data_pagamento
     * @param  string|null $observacao
     * @param  string|null $link_pdf
     * @param  bool|null $excluido
     */
    public function __construct(
        public ?int $cliente_id,
        public ?string $numero_documento,
        public ?float $valor,
        public ?string $data_vencimento,
        public ?string $pedido_id = null,
        public ?string $arquivo_pdf = null,
        public ?string $data_pagamento = null,
        public ?string $observacao = null,
        public ?string $link_pdf = null,
        public ?bool $excluido = null,
    ) {
    }

    /**
     * Create a new instance from an object.
     *
     * @param  object $object
     * @return static
     */
    public static function fromObject(object $object): static
    {
        return new static(
            cliente_id: $object->cliente_id,
            numero_documento: $object->numero_documento,
            valor: $object->valor,
            data_vencimento: $object->data_vencimento,
            pedido_id: $object->pedido_id,
            arquivo_pdf: $object->arquivo_pdf,
            data_pagamento: $object->data_pagamento,
            observacao: $object->observacao,
            link_pdf: $object->link_pdf,
            excluido: $object->excluido
        );
    }
}
