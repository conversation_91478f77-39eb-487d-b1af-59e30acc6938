APP_NAME=MercosFlex
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

TENANCY_CENTRAL_DOMAIN=

QI=
VE=

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=erpflex_mercos_main
DB_USERNAME=root
DB_PASSWORD=

QC_ERPFLEX_DB_CONNECTION=qc_erpflex_mysql
QC_ERPFLEX_DB_HOST=host
QC_ERPFLEX_DB_PORT=3306
QC_ERPFLEX_DB_DATABASE=database
QC_ERPFLEX_DB_USERNAME=user
QC_ERPFLEX_DB_PASSWORD=password

ERPFLEX_DB_CONNECTION=erpflex_mysql
ERPFLEX_DB_HOST=host
ERPFLEX_DB_PORT=3306
ERPFLEX_DB_DATABASE=database
ERPFLEX_DB_USERNAME=user
ERPFLEX_DB_PASSWORD=password

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# ERPFlex
ERP_FLEX_API_URL=https://sistema.erpflex.com.br

# Mercos
MERCOS_API_URL_PRODUCTION=https://app.mercos.com/api
MERCOS_API_URL_SANDBOX=https://sandbox.mercos.com/api
MERCOS_API_TOKEN_APPLICATION=
