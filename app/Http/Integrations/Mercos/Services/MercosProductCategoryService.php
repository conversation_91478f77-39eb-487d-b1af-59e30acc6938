<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosProductCategory;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosCategoryResponse;
use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\ProductCategory\MercosProductCategoryCreateProductCategoryRequest;
use App\Http\Integrations\Mercos\Requests\ProductCategory\MercosProductCategoryDeleteProductCategoryRequest;
use App\Http\Integrations\Mercos\Requests\ProductCategory\MercosProductCategoryGetProductCategoriesRequest;
use App\Http\Integrations\Mercos\Requests\ProductCategory\MercosProductCategoryGetProductCategoryRequest;
use App\Http\Integrations\Mercos\Requests\ProductCategory\MercosProductCategoryUpdateProductCategoryRequest;
use App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse;
use App\Http\Integrations\Mercos\Services\Interfaces\Getable;

class MercosProductCategoryService extends MercosBaseService implements Getable
{
    use HandlesLogCreation;

    /**
     * Get all product categories.
     *
     * @param  string|null $filters
     * @return \App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse
     */
    public function get(?string $filters = null): MercosGetCollectionDefaultResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosProductCategoryGetProductCategoriesRequest($filters))
            ->send();

        $this->createLog($response);

        $body = json_decode($response->body());

        if ($response->status() === 429) {
            $delayForRateLimiting = (int) $body->tempo_ate_permitir_novamente + 1;
        }

        return new MercosGetCollectionDefaultResponse(
            is_array($body) ? $body : [],
            $delayForRateLimiting,
            (int) $response->header('MEUSPEDIDOS_REQUISICOES_EXTRAS'),
            $response->successful()
        );
    }

    /**
     * Get a product category by its ID.
     *
     * @param  int $id
     * @return mixed
     */
    public function getById(int $id): mixed
    {
        return json_decode(
            (new MercosConnector($this->applicationToken, $this->companyToken))
                ->request(new MercosProductCategoryGetProductCategoryRequest($id))
                ->send()
        );
    }

    /**
     * Create a product category.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosProductCategory $mercosProductCategory
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosCategoryResponse
     */
    public function create(MercosProductCategory $mercosProductCategory): MercosCategoryResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $request = new MercosProductCategoryCreateProductCategoryRequest($mercosProductCategory);

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request($request)
            ->send();

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        $this->createLog(
            response: $response,
            success: $response->status() === 201,
            requestBody: $request->defaultData()
        );

        return new MercosCategoryResponse(
            mercosId: !empty($response->header('MeusPedidosID'))
                ? $response->header('MeusPedidosID')
                : null,
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 201
        );
    }

    /**
     * Update a product category.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosProductCategory $mercosProductCategory
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosCategoryResponse
     */
    public function update(int $id, MercosProductCategory $mercosProductCategory): MercosCategoryResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $request = new MercosProductCategoryUpdateProductCategoryRequest($id, $mercosProductCategory);

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request($request)
            ->send();

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        $this->createLog(
            response: $response,
            success: $response->status() === 200,
            requestBody: $request->defaultData()
        );

        return new MercosCategoryResponse(
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 200
        );
    }

    /**
     * Delete a product category.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosProductCategory $mercosProductCategory
     * @return bool
     */
    public function delete(int $id, MercosProductCategory $mercosProductCategory): bool
    {
        return (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosProductCategoryDeleteProductCategoryRequest($id, $mercosProductCategory))
            ->send()
            ->ok();
    }
}
