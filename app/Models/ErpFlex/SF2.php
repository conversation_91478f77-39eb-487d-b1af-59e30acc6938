<?php

namespace App\Models\ErpFlex;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

 /**
 * SF2 (Invoice headers) Model.
 *
 * @package App\Models\ErpFlex;
 *
 * @property  int $SF2_ID
 * @property  int $SF2_IDSC5
 * @property  int $SF2_IDSF3
 * @property  string $SF2_Emissao
 * @property  string $SF2_Doc
 * @property  string $SF2_NumSeq
 * @property  string $SF2_NrNFe
 */
class SF2 extends BaseModel
{
    protected $table = 'SF2';
    protected $primaryKey = 'SF2_ID';
    protected $prefix = 'SF2';

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'SF2_DT_INC' => 'datetime',
        'SF2_DT_ALT' => 'datetime',
    ];

    public function sc5s(): HasMany
    {
        return $this->hasMany(SC5::class, 'SC5_ID', 'SF2_IDSC5');
    }

    public function sf3(): HasOne
    {
        return $this->hasOne(SF3::class, 'SF3_ID', 'SF2_IDSF3');
    }

    public function sd2s(): HasMany
    {
        return $this->hasMany(SD2::class, 'SD2_IDSF2', 'SF2_ID');
    }
}
