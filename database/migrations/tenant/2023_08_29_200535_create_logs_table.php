<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('logs', function (Blueprint $table) {
            $table->id();
            $table->string('service_name')->index()->nullable();
            $table->string('service_method')->index()->nullable();
            $table->boolean('success')->nullable();
            $table->string('status_code', 3)->nullable();
            $table->json('response_headers')->nullable();
            $table->json('response_body')->nullable();
            $table->string('method', 10)->nullable();
            $table->text('url')->nullable();
            $table->json('request_headers')->nullable();
            $table->json('request_body')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('logs');
    }
};
