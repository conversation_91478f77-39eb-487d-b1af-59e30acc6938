<?php

namespace App\Http\Integrations\Mercos\Requests\Salesman;

use App\Http\Integrations\Mercos\Requests\MercosBaseGetRequest;

class MercosSalesmanGetSalesmanRequest extends MercosBaseGetRequest
{
    /**
     * Create a new instance.
     *
     * @param  int $id
     */
    public function __construct(protected int $id)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/usuarios/' . $this->id;
    }
}
