<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosBatchUpdatePriceTableProduct
{
    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosSinglePriceTableProduct[] $priceTableProducts
     */
    public function __construct(
        public array $priceTableProducts
    ) {
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return collect($this->priceTableProducts)
            ->map(fn (MercosSinglePriceTableProduct $mercosSinglePriceTableProduct): array => $mercosSinglePriceTableProduct->toArray())
            ->toArray();
    }
}
