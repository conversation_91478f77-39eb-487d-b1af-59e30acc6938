<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosPriceTableProduct
{
    /**
     * Create a new instance.
     *
     * @param  float $preco
     * @param  int $tabela_id
     * @param  int $produto_id
     * @param  int|null $id
     * @param  bool|null $excluido
     * @param  string|null $ultima_alteracao
     */
    public function __construct(
        public float $preco,
        public int $tabela_id,
        public int $produto_id,
        public ?int $id = null,
        public ?bool $excluido = null,
        public ?string $ultima_alteracao = null
    ) {
    }

    /**
     * @inheritDoc
     */
    public function toArray(bool $write = false): array
    {
        $data = [
            'id' => $this->id,
            'preco' => $this->preco,
            'tabela_id' => $this->tabela_id,
            'produto_id' => $this->produto_id,
            'excluido' => $this->excluido,
            'ultima_alteracao' => $this->ultima_alteracao
        ];

        if ($write) {
            unset($data['id']);
            unset($data['ultima_alteracao']);
        }

        return $data;
    }
}
