<?php

namespace App\Http\Integrations\ErpFlex\Requests\Product;

use App\Http\Integrations\ErpFlex\Requests\ErpFlexBaseGetRequest;

class ErpFlexProductGetProductsRequest extends ErpFlexBaseGetRequest
{
    /**
     * Create a new instance.
     *
     * @param  int|null $limit
     * @param  int|null $offset
     * @param  string|null $filters
     */
    public function __construct(
        protected ?int $limit = null,
        protected ?int $offset = null,
        protected ?string $filters = null
    ) {
        if (!is_null($this->limit) && $this->limit > 50) {
            $this->limit = 50;
        }

        $this->filters = $filters && ($limit || $offset)
            ? ('&' . $this->filters)
            : $this->filters;
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        if (!is_null($this->limit)) {
            return !is_null($this->offset)
                ? "/api/produtos?limit=$this->limit&offset=$this->offset$this->filters"
                : "/api/produtos?limit=$this->limit$this->filters";
        }

        if (!is_null($this->offset)) {
            return "/api/produtos?offset=$this->offset$this->filters";
        }

        return "/api/produtos$this->filters";
    }
}
