<?php

namespace App\Models\Concerns\Subcategory;

use App\Models\Category;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesSubcategoryRelationships
{
    /**
     * Load the category relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }
}
