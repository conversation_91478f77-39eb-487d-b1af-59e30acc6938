<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Order billing model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $order_id
 * @property  string $erp_flex_order_id
 * @property  int $billing_id
 * @property  string $erp_flex_billing_id
 * @property  string $mercos_billing_id
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Order $order
 * @property  \App\Models\Billing $billing
 */
class OrderBilling extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'order_id',
        'erp_flex_order_id',
        'billing_id',
        'erp_flex_billing_id',
        'mercos_billing_id',
    ];

    /**
     * Load the order relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Load the billing relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function billing(): BelongsTo
    {
        return $this->belongsTo(Billing::class);
    }
}
