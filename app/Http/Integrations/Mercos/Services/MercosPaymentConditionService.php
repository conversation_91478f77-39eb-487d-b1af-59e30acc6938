<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosPaymentCondition;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosPaymentConditionResponse;
use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\PaymentCondition\MercosPaymentConditionCreatePaymentConditionRequest;
use App\Http\Integrations\Mercos\Requests\PaymentCondition\MercosPaymentConditionDeletePaymentConditionRequest;
use App\Http\Integrations\Mercos\Requests\PaymentCondition\MercosPaymentConditionGetPaymentConditionRequest;
use App\Http\Integrations\Mercos\Requests\PaymentCondition\MercosPaymentConditionGetPaymentConditionsRequest;
use App\Http\Integrations\Mercos\Requests\PaymentCondition\MercosPaymentConditionUpdatePaymentConditionRequest;
use App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse;
use App\Http\Integrations\Mercos\Services\Interfaces\Getable;

class MercosPaymentConditionService extends MercosBaseService implements Getable
{
    use HandlesLogCreation;

    /**
     * Get all payment conditions.
     *
     * @param  string|null $filters
     * @return \App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse
     */
    public function get(?string $filters = null): MercosGetCollectionDefaultResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosPaymentConditionGetPaymentConditionsRequest($filters))
            ->send();

        $this->createLog($response);

        $body = json_decode($response->body());

        if ($response->status() === 429) {
            $delayForRateLimiting = (int) $body->tempo_ate_permitir_novamente + 1;
        }

        return new MercosGetCollectionDefaultResponse(
            is_array($body) ? $body : [],
            $delayForRateLimiting,
            (int) $response->header('MEUSPEDIDOS_REQUISICOES_EXTRAS'),
            $response->successful()
        );
    }

    /**
     * Get a payment condition by its ID.
     *
     * @param  int $id
     * @return mixed
     */
    public function getById(int $id): mixed
    {
        return json_decode(
            (new MercosConnector($this->applicationToken, $this->companyToken))
                ->request(new MercosPaymentConditionGetPaymentConditionRequest($id))
                ->send()
                ->body()
        );
    }

    /**
     * Create a payment condition.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPaymentCondition $mercosPaymentCondition
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosPaymentConditionResponse
     */
    public function create(MercosPaymentCondition $mercosPaymentCondition): MercosPaymentConditionResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosPaymentConditionCreatePaymentConditionRequest($mercosPaymentCondition))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosPaymentConditionResponse(
            mercosId: !empty($response->header('MeusPedidosID'))
            ? $response->header('MeusPedidosID')
            : null,
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 201
        );
    }

    /**
     * Update a payment condition.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPaymentCondition $mercosPaymentCondition
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosPaymentConditionResponse
     */
    public function update(int $id, MercosPaymentCondition $mercosPaymentCondition): MercosPaymentConditionResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosPaymentConditionUpdatePaymentConditionRequest($id, $mercosPaymentCondition))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosPaymentConditionResponse(
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 200
        );
    }

    /**
     * Delete a payment condition.
     *
     * @param  int $id
     * @return mixed
     */
    public function delete(int $id): mixed
    {
        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosPaymentConditionDeletePaymentConditionRequest($id))
            ->send();

        // @todo tratativa para o rate limit.

        return new MercosPaymentConditionResponse(mercosId: $id);
    }
}
