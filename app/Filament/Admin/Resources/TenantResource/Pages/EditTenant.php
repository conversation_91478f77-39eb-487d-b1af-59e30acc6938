<?php

namespace App\Filament\Admin\Resources\TenantResource\Pages;

use App\Filament\Admin\Resources\TenantResource;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditTenant extends EditRecord
{
    protected static string $resource = TenantResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data = array_merge($data, [
            'erp_flex_api_username' => base_decrypt($this->record->erp_flex['api']['username']),
            'erp_flex_api_password' => base_decrypt($this->record->erp_flex['api']['password']),
            'erp_flex_tenant_id' => $this->record->erp_flex['company_id'],
            'erp_flex_multi_database' => $this->record->erp_flex['multi_database'],
            'erp_flex_connection_mode' => $this->record->erp_flex['connection_mode'],
            'mercos_api_company_token' => base_decrypt($this->record->mercos['api']['company_token']),
            'mercos_connection_mode' => $this->record->mercos['connection_mode'],
        ]);

        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $record->update([
            'mercos' => [
                'api' => [
                    'company_token' => base_encrypt($data['mercos_api_company_token']),
                    'application_token' => config('mercos.api.token.application'),
                ],
                'connection_mode' => $data['mercos_connection_mode'],
            ],
            'erp_flex' => [
                'api' => [
                    'username' => base_encrypt($data['erp_flex_api_username']),
                    'password' => base_encrypt($data['erp_flex_api_password']),
                ],
                'company_id' => $data['erp_flex_tenant_id'],
                'multi_database' => $data['erp_flex_multi_database'],
                'connection_mode' => $data['erp_flex_connection_mode'],
            ],
        ]);

        return $record;
    }
}
