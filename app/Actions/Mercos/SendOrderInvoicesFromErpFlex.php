<?php

namespace App\Actions\Mercos;

use App\Http\Integrations\ErpFlex\Services\ErpFlexInvoiceService;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderInvoice;
use App\Http\Integrations\Mercos\Services\MercosInvoiceService;
use App\Models\ErpFlex\SF2;
use App\Models\Invoice;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class SendOrderInvoicesFromErpFlex
{
    use AsAction;

    protected bool $force = false;

    protected MercosInvoiceService $mercosInvoiceService;
    protected ErpFlexInvoiceService $erpFlexInvoiceService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(tenant('id') . '_cadastros');
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_orders_automation) {
            return;
        }

        $this->force = $force;
        $this->mercosInvoiceService = new MercosInvoiceService();
        $this->erpFlexInvoiceService = new ErpFlexInvoiceService();

        $this->sendInvoices();
    }

    /**
     * Send pending invoices.
     *
     * @return void
     */
    protected function sendInvoices(): void
    {
        try {
            Order::query()
                ->whereNotNull('invoice_id')
                ->where('invoices_sent', false)
                ->lazyById(1000)
                ->each(function (Order $order): void {
                    $this->processSingleOrder($order);
                });
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Process a single order.
     *
     * @param Order $order
     * @return void
     */
    protected function processSingleOrder(Order $order): void
    {
        $sf2Items = SF2::query()
            ->with('sc5', 'sf3')
            ->whereHas('sc5', function (Builder $query) use ($order): Builder {
                return $query->where('SC5_ID', $order->erp_flex_id);
            })
            ->whereHas('sf3', function (Builder $query) use ($order): Builder {
                return $query->where('SF3_GerouNFe', $order->erp_flex_id);
            })
            ->get();

        foreach ($sf2Items as $sf2) {
            /** @var \App\Models\ErpFlex\SF2 $sf2 */
            $this->sendMercosInvoice($order, $sf2);
        }
    }

    /**
     * Send invoices to Mercos.
     *
     * @param Order $order
     * @param SF2 $sf2
     * @return void
     */
    protected function sendMercosInvoice(Order $order, SF2 $sf2): void
    {
        $invoiceProcessed = false;

        $invoiceProcessed = true;

        $invoice = Invoice::updateOrCreate([
            'order_id' => $order->id,
            'erp_flex_invoice_id' => $sf2->SF2_ID,
        ]);

        $mercosObject = new MercosOrderInvoice(
            cliente_id: $order->mercos_data['cliente_id'],
            pedido_id: $order->mercos_id,
            numero: $sf2->sf3->SF3_NrNFSe ?? $sf2->SF2_NrNFe ?? null,
            serie: $sf2->sf3->SF3_Serie,
            chave_acesso: $sf2->sf3->SF3_CodAut22,
            valor: (float) $sf2->sf3->SF3_ValNF ?? 0,
            data_emissao: (new Carbon($sf2->SF2_Emissao))->format('Y-m-d'),
            arquivo_xml: $this->getInvoiceAttachment($sf2->SF2_ID)
        );

        $response = $this->mercosInvoiceService->createInvoice($mercosObject);

        sleep($response->delayForRateLimiting);

        if (!$response->success) {
            $response = $this->mercosInvoiceService->createInvoice($mercosObject);
        }

        if ($response->mercosId) {
            $invoice->update([
                'mercos_invoice_id' => $response->mercosId,
            ]);
        }

        if (!$invoiceProcessed) {
            return;
        }

        $order->update([
            'invoices_sent' => true,
        ]);
    }

    protected function getInvoiceAttachment(int $id): string|null
    {
        return $this->erpFlexInvoiceService->getById($id);
    }
}
