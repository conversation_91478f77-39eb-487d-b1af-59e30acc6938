<?php

namespace App\Filament\Resources\ProductResource\Pages;

use App\Actions\Mercos\CreatePricesTableFromErpFlex;
use App\Actions\Mercos\CreatePriceTableProductFromErpFlex;
use App\Actions\Mercos\CreateProductsFromErpFlex;
use App\Filament\Resources\ProductResource;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ManageRecords;
use Throwable;

class ManageProducts extends ManageRecords
{
    protected static string $resource = ProductResource::class;

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            Action::make('Mapear produtos')
                ->visible(!integration_settings()->products_pairing_finished)
                ->url(route('products.pairing'))
                ->icon('heroicon-o-cog')
                ->color('secondary'),
            Action::make('Forçar sincronização')
                ->visible(integration_settings()->products_pairing_finished)
                ->action(function (): void {
                    try {
                        CreateProductsFromErpFlex::dispatch(true);
                        CreatePricesTableFromErpFlex::dispatch(true);
                        CreatePriceTableProductFromErpFlex::dispatch(true);
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->color('warning')
                ->requiresConfirmation(),
            Action::make('Sincronizar')
                ->visible(integration_settings()->products_pairing_finished)
                ->action(function (): void {
                    try {
                        CreateProductsFromErpFlex::dispatch();
                        CreatePricesTableFromErpFlex::dispatch();
                        CreatePriceTableProductFromErpFlex::dispatch();
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->requiresConfirmation(),
        ];
    }
}
