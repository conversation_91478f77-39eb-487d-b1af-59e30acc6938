<?php

namespace App\Models\ErpFlex;

use Illuminate\Database\Eloquent\Relations\HasOne;

 /**
 * SE2 (receivables) Model.
 *
 * @package App\Models\ErpFlex;
 *
 * @property  int $SE2_IDSC5
 * @property  int $SE2_IDSF2
 */
class SE2 extends BaseModel
{
    protected $table = 'SE2';
    protected $primaryKey = 'SE2_ID';
    protected $prefix = 'SE2';

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'SE2_DT_INC' => 'datetime',
        'SE2_DT_ALT' => 'datetime',
    ];
}
