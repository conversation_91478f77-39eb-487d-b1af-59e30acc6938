<?php

namespace App\Actions\Mercos;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosProduct;
use App\Http\Integrations\Mercos\Services\MercosProductService;
use App\Models\Category;
use App\Models\ErpFlex\NCMTRB;
use App\Models\ErpFlex\SB2;
use App\Models\IntegrationSetting;
use App\Models\Product;
use App\Models\ProductPairing;
use App\Models\Subcategory;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateProductsFromErpFlex
{
    use AsAction;

    protected bool $willUpdateStock = false;
    protected bool $force = false;
    protected bool $pairing = false;

    protected MercosProductService $mercosProductService;
    protected IntegrationSetting $integrationSetting;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue('erp_flex_management_' . tenant('id'));
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @param  int|null $erpFlexId
     * @param  \App\Models\Product|null $product
     * @param  \App\Models\ErpFlex\SB2|null $sb2
     * @param  bool $reprocess
     * @return void
     */
    public function handle(
        bool $force = false,
        ?int $erpFlexId = null,
        Product $product = null,
        SB2 $sb2 = null,
    ): void {
        $this->integrationSetting = integration_settings();

        if (!$this->integrationSetting->enables_products_automation) {
            return;
        }

        $this->pairing = false;

        if (!$this->integrationSetting->products_pairing_finished) {
            $this->pairing = true;
        }

        $this->willUpdateStock = $this->integrationSetting->enables_stock_automation;

        $this->force = $force;
        $this->mercosProductService = new MercosProductService();

        // Used when it's when updating the price table.
        if (!is_null($product) && !is_null($sb2)) {
            if (!$product->mercos_id) {
                $this->createSingleProduct($product, $sb2);
            } else {
                $this->updateSingleProduct($product, $sb2);
            }

            return;
        }

        $this->integrateProducts($erpFlexId);
    }

    /**
     * Integrate all products from ERPFlex to Mercos.
     *
     * @param  int|null $erpFlexId
     * @return void
     */
    protected function integrateProducts(?int $erpFlexId = null): void
    {
        try {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();
            $executionStart = now();

            $updateStartedAt = $integrationSetting->last_products_update
                ? Carbon::parse($integrationSetting->last_products_update)->setTimezone('America/Sao_Paulo')
                : Carbon::parse('1900-01-01');

            SB2::query()
                ->with('SB1')
                ->whereHas('SB1', function (Builder $query): Builder {
                    return $query
                        ->whereIn('SB1_Tipo', ['PA', 'RV'])
                        ->where('SB1_AutomComercial', 'S');
                })
                ->when($erpFlexId, fn(Builder $query): Builder => $query->where('SB2_ID', $erpFlexId))
                ->when(!$this->force, function (Builder $query) use ($updateStartedAt): Builder {
                    return $query->where(function (Builder $query) use ($updateStartedAt): Builder {
                        return $query
                            ->where(function (Builder $query) use ($updateStartedAt): Builder {
                                return $query
                                    ->where('SB2_DT_INC', '>=', $updateStartedAt)
                                    ->orWhere('SB2_DT_ALT', '>=', $updateStartedAt);
                            })
                            ->orWhereHas('SB1', function (Builder $query) use ($updateStartedAt): Builder {
                                return $query->where(function (Builder $query) use ($updateStartedAt): Builder {
                                    return $query
                                        ->where('SB1_DT_INC', '>=', $updateStartedAt)
                                        ->orWhere('SB1_DT_ALT', '>=', $updateStartedAt);
                                });
                            });
                    });
                })
                ->lazyById(1000, 'SB2_ID')
                ->each(function (SB2 $sb2): void {
                    try {
                        $this->processSingleProduct($sb2);
                    } catch (Throwable $th) {
                        error($th);
                    }
                });

            $integrationSetting->update(['last_products_update' => $executionStart]);
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Process a single product.
     *
     * @param  \App\Models\ErpFlex\SB2 $sb2
     * @return void
     */
    protected function processSingleProduct(SB2 $sb2): void
    {
        $productData = [
            'erp_flex_data' => [
                'SB2_Codigo' => $sb2?->SB2_Codigo,
                'SB2_Ativo' => $sb2?->SB2_Ativo,
                'SB2_Peso' => $sb2?->SB2_Peso,
                'SB2_Largura' => $sb2?->SB2_Largura,
                'SB2_Altura' => $sb2?->SB2_Altura,
                'sb1' => [
                    'SB1_ID' => $sb2?->sb1?->SB1_ID,
                    'SB1_Codigo' => $sb2?->sb1?->SB1_Codigo,
                    'SB1_Caracteristicas' => $sb2?->sb1?->SB1_Caracteristicas,
                    'SB1_Desc' => $sb2?->sb1?->SB1_Desc,
                    'SB1_UM' => $sb2?->sb1?->SB1_UM,
                    'SB1_Tipo' => $sb2?->sb1?->SB1_Tipo,
                    'SB1_Ativo' => $sb2?->sb1?->SB1_Ativo,
                    'ncm' => [
                        'NCM_ID' => $sb2?->sb1?->ncm?->NCM_ID,
                        'NCM_Codigo' => $sb2?->sb1?->ncm?->NCM_Codigo,
                        'NCM_EX' => $sb2?->sb1?->ncm?->NCM_EX,
                        'ncmTrb' => $sb2?->sb1?->ncm->ncmTrb
                            ->map(fn(NCMTRB $ncmTrb): array => [
                                'NCM_TRB_ID' => $ncmTrb->NCM_TRB_ID,
                                'NCM_TRB_IDNCM' => $ncmTrb->NCM_TRB_IDNCM,
                                'NCM_TRB_FAT_PIPI' => $ncmTrb->NCM_TRB_FAT_PIPI,
                            ])
                            ->toArray(),
                    ],
                    'SBA' => [
                        'SBA_ID' => $sb2?->sb1?->sba?->SBA_ID,
                    ],
                ],
                'sbp' => [
                    'SBP_Preco' => $sb2->sbp->first()->SBP_Preco ?? null,
                ],
                'sb2sbw' => [
                    'SB2_SBW_QAtu' => $sb2->sb2sbw->first()->SB2_SBW_QAtu ?? null,
                ],
                'sb2DescItensVar' => [
                    'SB2_DESCITENSVAR_DescChaveItensVar' => $sb2?->sb2DescItensVar?->SB2_DESCITENSVAR_DescChaveItensVar,
                ],
            ]
        ];

        if ($this->pairing) {
            ProductPairing::updateOrCreate(
                ['erp_flex_id' => $sb2->SB2_ID],
                $productData
            );

            return;
        }

        /** @var \App\Models\Product $product */
        $product = Product::updateOrCreate(
            ['erp_flex_id' => $sb2->SB2_ID],
            $productData
        );

        if (!$product->mercos_id) {
            $this->createSingleProduct($product, $sb2);
            return;
        }

        $this->updateSingleProduct($product, $sb2);
    }

    /**
     * Create a single product.
     *
     * @param  \App\Models\Product $product
     * @param  \App\Models\ErpFlex\SB2 $sb2
     * @return void
     */
    protected function createSingleProduct(Product $product, SB2 $sb2): void
    {
        if ($this->integrationSetting->uses_category_for_mercos_products) {
            /** @var \App\Models\Category $category */
            $category = Category::query()
                ->where('erp_flex_id', $sb2->sb1->sba->SBA_IDPAI)
                ->first();

            $categoryId = $category?->mercos_id;
        } else {
            /** @var \App\Models\Subcategory $subcategory */
            $subcategory = Subcategory::query()
                ->where('erp_flex_id', $sb2->sb1->SB1_IDSBA)
                ->first();

            $categoryId = $subcategory?->mercos_id;
        }

        $code = $sb2->SB2_Codigo;

        if (
            $sb2->sb1->SB1_Codigo !== ''
            && !is_null($sb2->sb1->SB1_Codigo)
            && $sb2->SB2_Codigo !== ''
            && !is_null($sb2->SB2_Codigo)
            && trim($sb2->sb1->SB1_Codigo) !== trim($sb2->SB2_Codigo)
        ) {
            $code = trim($sb2->sb1->SB1_Codigo) . '-' . $sb2->SB2_Codigo;
        }

        $ncm = null;

        if (!is_null($sb2->sb1->ncm->NCM_Codigo) && $sb2->sb1->ncm->NCM_Codigo !== '') {
            $ncm = trim($sb2->sb1->ncm->NCM_Codigo);

            if (!is_null($sb2?->sb1?->ncm?->NCM_EX) && $sb2?->sb1?->ncm?->NCM_EX !== '') {
                $ncm .= ('-' . $sb2?->sb1?->ncm?->NCM_EX);
            }
        }

        $sbp = $sb2->sbp->first();

        $mercosProductObject = new MercosProduct(
            codigo: $code,
            nome: trim($sb2->sb1->SB1_Desc) . ' ' . trim($sb2->sb2DescItensVar->SB2_DESCITENSVAR_DescChaveItensVar ?? ''),
            preco_tabela: $sbp->SBP_Preco ?? 0,
            preco_minimo: $sbp->SBP_PrecoMinimo ?? 0,
            unidade: trim($sb2->sb1->SB1_UM),
            saldo_estoque: $this->defineStockAmount($sb2),
            codigo_ncm: $ncm,
            categoria_id: $categoryId,
            observacoes: $sb2->sb1->SB1_Caracteristicas && $sb2->sb1->SB1_Caracteristicas !== ''
                ? trim($sb2->sb1->SB1_Caracteristicas)
                : null,
            ipi: (float) $sb2->sb1->ncm->ncmTrb->first()->NCM_TRB_FAT_PIPI ?? 0,
            ativo: isset($sb2->sb1->SB1_Ativo) && $sb2->sb1->SB1_Ativo === 1
                && isset($sb2->SB2_Ativo) && $sb2->SB2_Ativo === 1,
            peso_bruto: isset($sb2->SB2_Peso) && $sb2->SB2_Peso !== ''
                ? $sb2->SB2_Peso
                : null,
            largura: isset($sb2->SB2_Largura) && $sb2->SB2_Largura !== ''
                ? $sb2->SB2_Largura
                : null,
            altura: isset($sb2->SB2_Altura) && $sb2->SB2_Altura !== ''
                ? $sb2->SB2_Altura
                : null,
            comprimento: isset($sb2->SB2_Comprimento) && $sb2->SB2_Comprimento !== ''
                ? $sb2->SB2_Comprimento
                : null
        );

        $response = $this->mercosProductService->create($mercosProductObject);

        sleep($response->delayForRateLimiting);

        if (is_null($response->mercosId)) {
            $response = $this->mercosProductService->create($mercosProductObject);
        }

        $product->update([
            'mercos_id' => $response->mercosId
        ]);
    }

    /**
     * Update a single product.
     *
     * @param  \App\Models\Product $product
     * @param  \App\Models\ErpFlex\SB2 $sb2
     * @return void
     */
    protected function updateSingleProduct(Product $product, SB2 $sb2): void
    {
        if ($this->integrationSetting->uses_category_for_mercos_products) {
            /** @var \App\Models\Category $category */
            $category = Category::query()
                ->where('erp_flex_id', $sb2->sb1->sba->SBA_IDPAI)
                ->first();

            $categoryId = $category?->mercos_id;
        } else {
            /** @var \App\Models\Subcategory $subcategory */
            $subcategory = Subcategory::query()
                ->where('erp_flex_id', $sb2->sb1->SB1_IDSBA)
                ->first();

            $categoryId = $subcategory?->mercos_id;
        }

        $code = $sb2->SB2_Codigo;

        if (
            $sb2->sb1->SB1_Codigo !== ''
            && !is_null($sb2->sb1->SB1_Codigo)
            && $sb2->SB2_Codigo !== ''
            && !is_null($sb2->SB2_Codigo)
            && trim($sb2->sb1->SB1_Codigo) !== trim($sb2->SB2_Codigo)
        ) {
            $code = trim($sb2->sb1->SB1_Codigo) . '-' . $sb2->SB2_Codigo;
        }

        $ncm = null;

        if (!is_null($sb2->sb1->ncm->NCM_Codigo) && !empty($sb2->sb1->ncm->NCM_Codigo)) {
            $ncm = trim($sb2->sb1->ncm->NCM_Codigo);

            if (!is_null($sb2?->sb1?->ncm?->NCM_EX) && $sb2?->sb1?->ncm?->NCM_EX !== '') {
                $ncm .= ('-' . $sb2?->sb1?->ncm?->NCM_EX);
            }
        }

        $sbp = $sb2->sbp->first();

        $mercosProductObject = new MercosProduct(
            id: $product->mercos_id,
            codigo: $code,
            nome: trim($sb2->sb1->SB1_Desc) . ' ' . trim($sb2->sb2DescItensVar->SB2_DESCITENSVAR_DescChaveItensVar ?? ''),
            preco_tabela: $sbp->SBP_Preco ?? 0,
            preco_minimo: $sbp->SBP_PrecoMinimo ?? 0,
            unidade: trim($sb2->sb1->SB1_UM),
            saldo_estoque: $this->defineStockAmount($sb2),
            codigo_ncm: $ncm,
            categoria_id: $categoryId,
            observacoes: $sb2->sb1->SB1_Caracteristicas && $sb2->sb1->SB1_Caracteristicas !== ''
                ? trim($sb2->sb1->SB1_Caracteristicas)
                : null,
            ipi: (float) $sb2->sb1->ncm->ncmTrb->first()->NCM_TRB_FAT_PIPI ?? 0,
            ativo: isset($sb2->sb1->SB1_Ativo) && $sb2->sb1->SB1_Ativo === 1
                && isset($sb2->SB2_Ativo) && $sb2->SB2_Ativo === 1,
            peso_bruto: isset($sb2->SB2_Peso) && $sb2->SB2_Peso !== ''
                ? $sb2->SB2_Peso
                : null,
            largura: isset($sb2->SB2_Largura) && $sb2->SB2_Largura !== ''
                ? $sb2->SB2_Largura
                : null,
            altura: isset($sb2->SB2_Altura) && $sb2->SB2_Altura !== ''
                ? $sb2->SB2_Altura
                : null,
            comprimento: isset($sb2->SB2_Comprimento) && $sb2->SB2_Comprimento !== ''
                ? $sb2->SB2_Comprimento
                : null
        );

        $response = $this->mercosProductService->update($product->mercos_id, $mercosProductObject);

        sleep($response->delayForRateLimiting);

        if (!$response->success) {
            $response = $this->mercosProductService->update($product->mercos_id, $mercosProductObject);
        }
    }

    /**
     * Define the product's stock amount.
     *
     * @param  \App\Models\ErpFlex\SB2 $sb2
     * @return float|null
     */
    protected function defineStockAmount(SB2 $sb2): ?float
    {
        if (!$this->willUpdateStock) {
            return null;
        }

        $totalItemBalance = $this->integrationSetting->uses_only_default_warehouse_in_stock_calculations
            ? (float) $sb2->sb2Sbw()->where('SB2_SBW_IDSBW', 0)->first()->SB2_SBW_QAtu
            : (float) $sb2->SB2_QAtu;

        $balance = $this->integrationSetting->uses_reserved_quantity_in_stock_calculations
            ? round(($totalItemBalance - (float) $sb2->SB2_Reserva), 4)
            : round($totalItemBalance, 4);

        return $balance;
    }
}
