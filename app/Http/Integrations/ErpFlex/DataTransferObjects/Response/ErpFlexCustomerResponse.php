<?php

namespace App\Http\Integrations\ErpFlex\DataTransferObjects\Response;

class ErpFlexCustomerResponse
{
    /**
     * Create a new instance.
     *
     * @param  string|null $erpFlexId
     * @param  bool|null $success
     * @param  string|null $message
     */
    public function __construct(
        public ?string $erpFlexId = null,
        public ?bool $success = null,
        public ?string $message = null
    ) {
    }
}
