{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "awcodes/filament-table-repeater": "^3.0", "barryvdh/laravel-debugbar": "^3.10", "filament/filament": "^3.2", "guzzlehttp/guzzle": "^7.8", "imanghafoori/laravel-masterpass": "^2.2", "konnco/filament-import": "^2.0.2-beta", "laravel/framework": "^10.43", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.9", "lorisleiva/laravel-actions": "^2.7", "novadaemon/filament-pretty-json": "^2.0", "sammyjo20/saloon-laravel": "^1.6", "stancl/tenancy": "^3.7"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.13", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Support/Functions/datetime.php", "app/Support/Functions/encryption.php", "app/Support/Functions/erp_flex.php", "app/Support/Functions/error.php", "app/Support/Functions/integration_setting.php", "app/Support/Functions/mercos.php", "app/Support/Functions/notifications.php", "app/Support/Functions/text.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}