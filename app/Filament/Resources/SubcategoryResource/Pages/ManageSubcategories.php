<?php

namespace App\Filament\Resources\SubcategoryResource\Pages;

use App\Filament\Resources\SubcategoryResource;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Support\Facades\Artisan;
use Throwable;

class ManageSubcategories extends ManageRecords
{
    protected static string $resource = SubcategoryResource::class;

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            Action::make('Mapear subcategorias')
                ->visible(!integration_settings()->subcategories_pairing_finished)
                ->url(route('subcategories.pairing'))
                ->icon('heroicon-o-cog')
                ->color('secondary'),
            Action::make('Forçar sincronização')
            ->visible(integration_settings()->subcategories_pairing_finished)
                ->action(function (): void {
                    try {
                        Artisan::call(SyncCustomers::class, ['force' => true]);
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->color('warning')
                ->requiresConfirmation(),
            Action::make('Sincronizar')
                ->visible(integration_settings()->subcategories_pairing_finished)
                ->action(function (): void {
                    try {
                        Artisan::call(SyncCustomers::class);
                        success_notification('O processo está rodando em segundo plano.')->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                })
                ->icon('heroicon-s-arrow-path')
                ->requiresConfirmation(),
        ];
    }
}
