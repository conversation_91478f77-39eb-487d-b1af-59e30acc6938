<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosHaulier;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosHaulierResponse;
use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\Haulier\MercosHaulierCreateHaulierRequest;
use App\Http\Integrations\Mercos\Requests\Haulier\MercosHaulierDeleteHaulierRequest;
use App\Http\Integrations\Mercos\Requests\Haulier\MercosHaulierGetHaulierRequest;
use App\Http\Integrations\Mercos\Requests\Haulier\MercosHaulierGetHauliersRequest;
use App\Http\Integrations\Mercos\Requests\Haulier\MercosHaulierUpdateHaulierRequest;
use App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse;
use App\Http\Integrations\Mercos\Services\Interfaces\Getable;

class MercosHaulierService extends MercosBaseService implements Getable
{
    use HandlesLogCreation;

    /**
     * Get all hauliers.
     *
     * @param  string|null $filters
     * @return \App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse
     */
    public function get(?string $filters = null): MercosGetCollectionDefaultResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosHaulierGetHauliersRequest($filters))
            ->send();

        $this->createLog($response);

        $body = json_decode($response->body());

        if ($response->status() === 429) {
            $delayForRateLimiting = (int) $body->tempo_ate_permitir_novamente + 1;
        }

        return new MercosGetCollectionDefaultResponse(
            is_array($body) ? $body : [],
            $delayForRateLimiting,
            (int) $response->header('MEUSPEDIDOS_REQUISICOES_EXTRAS'),
            $response->successful()
        );
    }

    /**
     * Get a haulier by its ID.
     *
     * @param  int $id
     * @return mixed
     */
    public function getById(int $id): mixed
    {
        return json_decode(
            (new MercosConnector($this->applicationToken, $this->companyToken))
                ->request(new MercosHaulierGetHaulierRequest($id))
                ->send()
                ->body()
        );
    }

    /**
     * Create a haulier.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosHaulier $mercosHaulier
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosHaulierResponse
     */
    public function create(MercosHaulier $mercosHaulier): MercosHaulierResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosHaulierCreateHaulierRequest($mercosHaulier))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosHaulierResponse(
            mercosId: !empty($response->header('MeusPedidosID'))
                ? $response->header('MeusPedidosID')
                : null,
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 201
        );
    }

    /**
     * Update a haulier.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosHaulier $mercosHaulier
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosHaulierResponse
     */
    public function update(int $id, MercosHaulier $mercosHaulier): MercosHaulierResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosHaulierUpdateHaulierRequest($id, $mercosHaulier))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosHaulierResponse(
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 200
        );
    }

    /**
     * Delete a haulier.
     *
     * @param  int $id
     * @param  string $name
     * @return mixed
     */
    public function delete(int $id, string $name): mixed
    {
        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosHaulierDeleteHaulierRequest($id, $name))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosHaulierResponse(
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 200
        );
    }
}
