<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\User\MercosUserGetUsersRequest;
use App\Http\Integrations\Mercos\Services\MercosBaseService;

class MercosUserService extends MercosBaseService
{
    /**
     * Get all users.
     *
     * @return mixed
     */
    public function get(): mixed
    {
        return json_decode(
            (new MercosConnector($this->applicationToken, $this->companyToken))
                ->request(new MercosUserGetUsersRequest())
                ->send()
        );
    }
}
