<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosOrderStatus
{
    /**
     * Create a new instance.
     *
     * @param  string|null $nome
     * @param  int|null $id
     * @param  string|null $cidade
     * @param  string|null $estado
     * @param  string|null $informacoes_adicionais
     * @param  array|null $telefones
     * @param  bool|null $excluido
     */
    public function __construct(
        public int $pedido_id,
        public float $valor_faturado,
        public string $data_faturamento,
        public ?string $numero_nf = null,
        public ?string $informacoes_adicionais = null,
        public ?bool $excluido = null,
    ) {
    }

    /**
     * Create a new instance from an object.
     *
     * @param  object $object
     * @return static
     */
    public static function fromObject(object $object): static
    {
        return new static(
            pedido_id: $object->pedido_id,
            valor_faturado: $object->valor_faturado,
            data_faturamento: $object->data_faturamento,
            numero_nf: $object->numero_nf,
            informacoes_adicionais: $object->informacoes_adicionais,
            excluido: $object->excluido
        );
    }
}
