<?php

namespace App\Http\Integrations\Mercos;

use Sammyjo20\Saloon\Http\SaloonConnector;
use Sammyjo20\Saloon\Traits\Plugins\AcceptsJson;

class MercosConnector extends SaloonConnector
{
    use AcceptsJson;

    /**
     * Create a new instance.
     *
     * @param  string $applicationToken
     * @param  string $companyToken
     */
    public function __construct(
        protected string $applicationToken,
        protected string $companyToken
    ) {
    }

    /**
     * The Base URL of the API.
     *
     * @return string
     */
    public function defineBaseUrl(): string
    {
        return app()->isProduction() && tenant()->mercos['connection_mode'] === 'production'
            ? config('mercos.api.url.production')
            : config('mercos.api.url.sandbox');
    }

    /**
     * The headers that will be applied to every request.
     *
     * @return string[]
     */
    public function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'ApplicationToken' => $this->applicationToken,
            'CompanyToken' => $this->companyToken
        ];
    }

    /**
     * The config options that will be applied to every request.
     *
     * @return string[]
     */
    public function defaultConfig(): array
    {
        return [];
    }
}
