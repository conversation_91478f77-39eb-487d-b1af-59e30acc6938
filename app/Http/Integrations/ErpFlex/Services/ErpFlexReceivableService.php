<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\ErpFlex\ErpFlexConnector;
use App\Http\Integrations\ErpFlex\Requests\Receivable\ErpFlexCustomerGetReceivableRequest;
use Exception;

class ErpFlexReceivableService extends ErpFlexBaseService
{
    use HandlesLogCreation;

    /**
     * Get a receivable by its ID.
     *
     * @param  int $id
     * @return mixed
     */
    public function getById(int $id, int $banco, int $carteira): string | null
    {
        $response = (new ErpFlexConnector($this->username, $this->password))
            ->request(new ErpFlexCustomerGetReceivableRequest($id, $banco, $carteira))
            ->send();

        $body = json_decode($response->body());

        if (is_null($body)) {
            $this->createLog(
                $response,
                false,
                'erp_flex_receivable_service.get_by_id.empty_body'
            );

            throw new Exception('erp_flex_receivable_service.get_by_id.empty_body');
        }

        $this->createLog(
            $response,
            !empty($body->pdfbase64)
        );

        return $body->pdfbase64;
    }
}
