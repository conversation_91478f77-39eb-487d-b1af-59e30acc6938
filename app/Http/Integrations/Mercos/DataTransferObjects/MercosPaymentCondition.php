<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosPaymentCondition
{
    /**
     * Create a new instance.
     *
     * @param  string|null $nome
     * @param  int|null $id
     * @param  float|null $valor_minimo
     * @param  bool|null $disponivel_b2b
     * @param  bool|null $excluido
     * @param  string|null $ultima_alteracao
     */
    public function __construct(
        public ?string $nome,
        public ?int $id = null,
        public ?float $valor_minimo = null,
        public ?bool $disponivel_b2b = null,
        public ?bool $excluido = null,
        public ?string $ultima_alteracao = null
    ) {
    }

    /**
     * Create a new instance from an object.
     *
     * @param  object $object
     * @return static
     */
    public static function fromObject(object $object): static
    {
        return new static(
            nome: $object->nome,
            id: $object->id,
            valor_minimo: $object->valor_minimo,
            disponivel_b2b: $object->disponivel_b2b,
            excluido: $object->excluido,
            ultima_alteracao: $object->ultima_alteracao,
        );
    }
}
