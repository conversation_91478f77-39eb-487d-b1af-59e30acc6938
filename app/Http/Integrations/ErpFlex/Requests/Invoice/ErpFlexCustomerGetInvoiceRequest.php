<?php

namespace App\Http\Integrations\ErpFlex\Requests\Invoice;

use App\Http\Integrations\ErpFlex\Requests\ErpFlexBaseGetRequest;

class ErpFlexCustomerGetInvoiceRequest extends ErpFlexBaseGetRequest
{
    /**
     * Create a new instance.
     *
     * @param  int $id
     */
    public function __construct(
        protected ?int $id
    ) {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return "/api_v2/faturamento/$this->id/xml";
    }
}
