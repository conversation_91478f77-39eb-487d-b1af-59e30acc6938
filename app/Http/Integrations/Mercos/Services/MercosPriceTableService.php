<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTable;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosPriceTableResponse;
use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\PriceTable\MercosPriceTableCreatePriceTableRequest;
use App\Http\Integrations\Mercos\Requests\PriceTable\MercosPriceTableDeletePriceTableRequest;
use App\Http\Integrations\Mercos\Requests\PriceTable\MercosPriceTableGetPriceTableRequest;
use App\Http\Integrations\Mercos\Requests\PriceTable\MercosPriceTableGetPriceTablesRequest;
use App\Http\Integrations\Mercos\Requests\PriceTable\MercosPriceTableUpdatePriceTableRequest;

class MercosPriceTableService extends MercosBaseService
{
    use HandlesLogCreation;

    /**
     * Get all price tables.
     *
     * @return mixed
     */
    public function get(): mixed
    {
        return json_decode(
            (new MercosConnector($this->applicationToken, $this->companyToken))
                ->request(new MercosPriceTableGetPriceTablesRequest())
                ->send()
        );
    }

    /**
     * Get a price table by its ID.
     *
     * @param  int $id
     * @return mixed
     */
    public function getById(int $id): mixed
    {
        return json_decode(
            (new MercosConnector($this->applicationToken, $this->companyToken))
                ->request(new MercosPriceTableGetPriceTableRequest($id))
                ->send()
        );
    }

    /**
     * Create a price table.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTable $mercosPriceTable
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosPriceTableResponse
     */
    public function create(MercosPriceTable $mercosPriceTable): MercosPriceTableResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosPriceTableCreatePriceTableRequest($mercosPriceTable))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosPriceTableResponse(
            mercosId: !empty($response->header('MeusPedidosID'))
            ? $response->header('MeusPedidosID')
            : null,
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 201
        );
    }

    /**
     * Update a price table.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTable $mercosPriceTable
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosPriceTableResponse
     */
    public function update(int $id, MercosPriceTable $mercosPriceTable): MercosPriceTableResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosPriceTableUpdatePriceTableRequest($id, $mercosPriceTable))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosPriceTableResponse(
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 200
        );
    }

    /**
     * Delete a price table.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTable $mercosPriceTable
     * @return bool
     */
    public function delete(int $id, MercosPriceTable $mercosPriceTable): bool
    {
        return (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosPriceTableDeletePriceTableRequest($id, $mercosPriceTable))
            ->send()
            ->ok();
    }
}
