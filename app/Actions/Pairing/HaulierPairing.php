<?php

namespace App\Actions\Pairing;

use App\Http\Integrations\Mercos\Services\MercosHaulierService;
use App\Models\Haulier;
use App\Models\HaulierPairing as HaulierPairingModel;
use App\Models\IntegrationSetting;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class HaulierPairing
{
    use AsAction;

    protected MercosHaulierService $mercosHaulierService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(tenant('id') . '_cadastros');
    }

    /**
     * Handle the action.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            if (integration_settings()->hauliers_pairing_finished) {
                throw new Exception('hauliers_pairing.pairing_should_only_be_done_once');
                return;
            }

            $pairingCollection = HaulierPairingModel::get();

            $pairingCollection->each(function (HaulierPairingModel $pairing): void {
                if (!$pairing->mercos_id) {
                    return;
                }

                Haulier::create([
                    'erp_flex_id' => $pairing->erp_flex_id,
                    'erp_flex_data' => $pairing->erp_flex_data,
                    'mercos_id' => $pairing->mercos_id,
                    'mercos_data' => $pairing->mercos_data,
                ]);
            });

            $integrationSetting = IntegrationSetting::first();
            $integrationSetting->hauliers_pairing_finished = true;
            $integrationSetting->save();
        } catch (Throwable $th) {
            error($th);
        }
    }
}
