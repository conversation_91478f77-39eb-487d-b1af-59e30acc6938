<?php

namespace App\Actions\Mercos;

use App\Actions\Mercos\Concerns\HandlesMercosDefaultApiCollectionGetRequests;
use App\Http\Integrations\Mercos\Services\MercosCustomerService;
use App\Models\IntegrationSetting;
use App\Models\MercosCustomer;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateMercosCustomersFromMercos
{
    use AsAction;
    use HandlesMercosDefaultApiCollectionGetRequests;

    protected bool $force = false;

    protected MercosCustomerService $mercosCustomerService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(tenant('id') . '_cadastros');
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_customer_automation) {
            return;
        }

        $this->force = $force;

        $this->mercosCustomerService = new MercosCustomerService();

        $this->integrateCustomers();
    }

    /**
     * Integrate all customers Mercos for pairing.
     *
     * @return void
     */
    protected function integrateCustomers(): void
    {
        try {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();
            $executionStart = now();

            $mercosApiCustomers = $this->getCustomersCollection();

            foreach ($mercosApiCustomers as $mercosApiCustomer) {
                $this->processSingleCustomer($mercosApiCustomer);
            }

            $integrationSetting->update(['last_customers_update' => $executionStart]);
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Get the customers collection for a given offset page.
     *
     * @return \Illuminate\Support\Collection
     */
    protected function getCustomersCollection(): Collection
    {
        $lastUpdatedAt = Carbon::parse(integration_settings()->last_customers_update)
            ->setTimezone('America/Sao_Paulo')->format('Y-m-d H:i:s');

        return $this->getMercosApiCollection($this->mercosCustomerService, $lastUpdatedAt);
    }

    /**
     * Process a single customer.
     *
     * @param  mixed $mercosApiCustomer
     * @return void
     */
    protected function processSingleCustomer(mixed $mercosApiCustomer): void
    {
        /** @var \App\Models\Customer $customer */
        $customer = MercosCustomer::updateOrCreate([
            'mercos_id' => $mercosApiCustomer->id,
        ], [
            'mercos_data' => json_decode(json_encode($mercosApiCustomer), true),
        ]);
    }
}
