<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderStatus;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosUpdateOrderResponse;
use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\Order\MercosOrderGetOrderRequest;
use App\Http\Integrations\Mercos\Requests\Order\MercosOrderGetOrdersRequest;
use App\Http\Integrations\Mercos\Requests\Order\MercosCreateOrderBillingRequest;
use App\Http\Integrations\Mercos\Requests\Order\MercosUpdateOrderBillingRequest;
use App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse;
use App\Http\Integrations\Mercos\Services\Interfaces\Getable;
use Carbon\Carbon;

class MercosOrderService extends MercosBaseService implements Getable
{
    use HandlesLogCreation;

    /**
     * Get all orders.
     *
     * @param  string|null $filters
     * @return \App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse
     */
    public function get(?string $filters = null): MercosGetCollectionDefaultResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosOrderGetOrdersRequest($filters))
            ->send();

        $this->createLog($response);

        $body = json_decode($response->body());

        if ($response->status() === 429) {
            $delayForRateLimiting = (int) $body->tempo_ate_permitir_novamente + 1;
        }

        return new MercosGetCollectionDefaultResponse(
            is_array($body) ? $body : [],
            $delayForRateLimiting,
            (int) $response->header('MEUSPEDIDOS_REQUISICOES_EXTRAS'),
            $response->successful()
        );
    }

    /**
     * Get an order by its ID.
     *
     * @param  int $id
     * @return mixed
     */
    public function getById(int $id): mixed
    {
        return json_decode(
            (new MercosConnector($this->applicationToken, $this->companyToken))
                ->request(new MercosOrderGetOrderRequest($id))
                ->send()
        );
    }

    /**
     * Get all orders starting from a given date.
     *
     * @param  \Carbon\Carbon $dateFrom
     * @return mixed
     */
    public function getAfter(Carbon $dateFrom): mixed
    {
        return json_decode(
            (new MercosConnector($this->applicationToken, $this->companyToken))
                ->request(new MercosOrderGetOrdersRequest('alterado_apos=' . $dateFrom->format('Y-m-d H:i:s')))
                ->send()
        );
    }

    /**
     * Create a billing from an order.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderStatus $mercosOrderStatus
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosUpdateOrderResponse
     */
    public function createBilling(MercosOrderStatus $mercosOrderStatus): MercosUpdateOrderResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosCreateOrderBillingRequest($mercosOrderStatus))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosUpdateOrderResponse(
            mercosId: !empty($response->header('MeusPedidosID'))
                ? $response->header('MeusPedidosID')
                : null,
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 201
        );
    }

    /**
     * Updates a billing from an order.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderStatus $mercosOrderStatus
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosUpdateOrderResponse
     */
    public function updateBilling(int $id, MercosOrderStatus $mercosOrderStatus): MercosUpdateOrderResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosUpdateOrderBillingRequest($id, $mercosOrderStatus))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosUpdateOrderResponse(
            mercosId: !empty($response->header('MeusPedidosID'))
                ? $response->header('MeusPedidosID')
                : null,
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 200
        );
    }
}
