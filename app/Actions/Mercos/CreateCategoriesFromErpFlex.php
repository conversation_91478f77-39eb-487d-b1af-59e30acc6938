<?php

namespace App\Actions\Mercos;

use App\Http\Integrations\ErpFlex\Services\ErpFlexCategoryService;
use App\Http\Integrations\ErpFlex\Services\ErpFlexSubcategoryService;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosProductCategory;
use App\Http\Integrations\Mercos\Services\MercosProductCategoryService;
use App\Models\Category;
use App\Models\CategoryPairing;
use App\Models\ErpFlex\SBA;
use App\Models\IntegrationSetting;
use App\Models\Subcategory;
use App\Models\SubcategoryPairing;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateCategoriesFromErpFlex
{
    use AsAction;

    protected bool $force = false;
    protected bool $pairing = false;

    protected ErpFlexCategoryService $erpFlexCategoryService;
    protected ErpFlexSubcategoryService $erpFlexSubcategoryService;
    protected MercosProductCategoryService $mercosProductCategoryService;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue('erp_flex_management_' . tenant('id'));
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_products_automation) {
            return;
        }

        $this->pairing = false;

        if (
            !integration_settings()->categories_pairing_finished
            && !integration_settings()->subcategories_pairing_finished
        ) {
            $this->pairing = true;
        }

        $this->force = $force;

        $this->erpFlexCategoryService = new ErpFlexCategoryService();
        $this->erpFlexSubcategoryService = new ErpFlexSubcategoryService();
        $this->mercosProductCategoryService = new MercosProductCategoryService();

        /** @var \App\Models\IntegrationSetting $integrationSetting */
        $integrationSetting = IntegrationSetting::first();
        $executionStart = now();

        $this->integrateCategories($integrationSetting);
        $this->integrateSubcategories($integrationSetting);

        $integrationSetting->update(['last_categories_update' => $executionStart]);
    }

    /**
     * Integrate all categories from ERPFlex to Mercos.
     *
     * @return void
     */
    protected function integrateCategories(IntegrationSetting $integrationSetting): void
    {
        try {
            $updateStartedAt = Carbon::parse($integrationSetting->last_categories_update)
                ->setTimezone('America/Sao_Paulo');

            SBA::query()
                ->where('SBA_IDPAI', 0)
                ->where('SBA_AutomComercial', 'S')
                ->when(!$this->force, function (Builder $query) use ($updateStartedAt): Builder {
                    return $query
                        ->where(function (Builder $query) use ($updateStartedAt): Builder {
                            return $query
                                ->where('SBA_DT_INC', '>=', $updateStartedAt)
                                ->orWhere('SBA_DT_ALT', '>=', $updateStartedAt);
                        });
                })
                ->lazyById(1000)
                ->each(function (SBA $erpFlexApiCategories): void {
                    try {
                        $this->processSingleCategory($erpFlexApiCategories);
                    } catch (Throwable $th) {
                        error($th);
                    }
                });
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Process a single category.
     *
     * @param  \App\Models\ErpFlex\SBA $erpFlexApiCategory
     * @return void
     */
    protected function processSingleCategory(SBA $erpFlexApiCategory): void
    {
        $categoryData = [
            'SBA_ID' => $erpFlexApiCategory->SBA_ID,
            'SBA_Desc' => $erpFlexApiCategory->SBA_Desc,
            'SBA_AutomComercial' => $erpFlexApiCategory->SBA_AutomComercial,
        ];

        if ($this->pairing) {
            CategoryPairing::updateOrCreate(
                ['erp_flex_id' => $erpFlexApiCategory->SBA_ID],
                ['erp_flex_data' => $categoryData]
            );

            return;
        }

        /** @var \App\Models\Category $category */
        $category = Category::updateOrCreate(
            ['erp_flex_id' => $erpFlexApiCategory->SBA_ID],
            ['erp_flex_data' => $categoryData]
        );

        if (!$category->mercos_id) {
            $this->createSingleCategory($category, $erpFlexApiCategory);
            return;
        }

        $this->updateSingleCategory($category, $erpFlexApiCategory);
    }

    /**
     * Create a single category.
     *
     * @param  \App\Models\Category $category
     * @param  \App\Models\ErpFlex\SBA $erpFlexApiCategory
     * @return void
     */
    protected function createSingleCategory(Category $category, SBA $erpFlexApiCategory): void
    {
        $mercosCategoryObject = new MercosProductCategory(
            nome: $erpFlexApiCategory->SBA_Desc
        );

        $response = $this->mercosProductCategoryService->create($mercosCategoryObject);

        sleep($response->delayForRateLimiting);

        if (is_null($response->mercosId)) {
            $response = $this->mercosProductCategoryService->create($mercosCategoryObject);
        }

        $category->update([
            'mercos_id' => $response->mercosId
        ]);
    }

    /**
     * Update a single category.
     *
     * @param  \App\Models\Category $category
     * @param  \App\Models\ErpFlex\SBA $erpFlexApiCategory
     * @return void
     */
    protected function updateSingleCategory(Category $category, SBA $erpFlexApiCategory): void
    {
        $mercosCategoryObject = new MercosProductCategory(
            nome: $erpFlexApiCategory->SBA_Desc,
            id: $category->mercos_id
        );

        $response = $this->mercosProductCategoryService->update($category->mercos_id, $mercosCategoryObject);

        sleep($response->delayForRateLimiting);

        if (!$response->success) {
            $response = $this->mercosProductCategoryService->update($category->mercos_id, $mercosCategoryObject);
        }
    }

    /**
     * Integrate all subcategories from ERPFlex to Mercos.
     *
     * @return void
     */
    protected function integrateSubcategories(IntegrationSetting $integrationSetting): void
    {
        try {
            $updateStartedAt = Carbon::parse($integrationSetting->last_categories_update)
                ->setTimezone('America/Sao_Paulo');

            SBA::query()
                ->where('SBA_IDPAI', '>', 0)
                ->whereRelation('sbaPai', 'SBA_AutomComercial', 'S')
                ->when(!$this->force, function (Builder $query) use ($updateStartedAt): Builder {
                    return $query
                        ->where(function (Builder $query) use ($updateStartedAt): Builder {
                            return $query
                                ->where('SBA_DT_INC', '>=', $updateStartedAt)
                                ->orWhere('SBA_DT_ALT', '>=', $updateStartedAt);
                        });
                })
                ->lazyById(1000)
                ->each(function (SBA $erpFlexApiSubcategory): void {
                    try {
                        $this->processSingleSubcategory($erpFlexApiSubcategory);
                    } catch (Throwable $th) {
                        throw_error($th);
                    }
                });
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Process a single subcategory.
     *
     * @param  \App\Models\ErpFlex\SBA $erpFlexApiSubcategory
     * @return void
     */
    protected function processSingleSubcategory(SBA $erpFlexApiSubcategory): void
    {
        $subcategoryData = [
            'erp_flex_data' => [
                'SBA_ID' => $erpFlexApiSubcategory->SBA_ID,
                'SBA_IDPAI' => $erpFlexApiSubcategory->SBA_IDPAI,
                'SBA_Desc' => $erpFlexApiSubcategory->SBA_Desc,
                'SBA_AutomComercial' => $erpFlexApiSubcategory->SBA_AutomComercial,
            ],
            'category_id' => Category::query()
                ->where('erp_flex_id', $erpFlexApiSubcategory->SBA_IDPAI)
                ->first()
                ?->id
        ];

        if ($this->pairing) {
            SubcategoryPairing::updateOrCreate(
                ['erp_flex_id' => $erpFlexApiSubcategory->SBA_ID],
                $subcategoryData,
            );

            return;
        }

        /** @var \App\Models\Subcategory $subcategory */
        $subcategory = Subcategory::updateOrCreate(
            ['erp_flex_id' => $erpFlexApiSubcategory->SBA_ID],
            $subcategoryData,
        );

        if (!$subcategory->mercos_id) {
            $this->createSingleSubcategory($subcategory, $erpFlexApiSubcategory);
            return;
        }

        $this->updateSingleSubcategory($subcategory, $erpFlexApiSubcategory);
    }

    /**
     * Create a single subcategory.
     *
     * @param  \App\Models\Subcategory $subcategory
     * @param  \App\Models\ErpFlex\SBA $erpFlexApiSubcategory
     * @return void
     */
    protected function createSingleSubcategory(Subcategory $subcategory, SBA $erpFlexApiSubcategory): void
    {
        $mercosProductCategory = new MercosProductCategory(
            nome: $erpFlexApiSubcategory->SBA_Desc,
            categoria_pai_id: $subcategory->category->mercos_id ?? null
        );

        $response = $this->mercosProductCategoryService->create($mercosProductCategory);

        sleep($response->delayForRateLimiting);

        if (is_null($response->mercosId)) {
            $response = $this->mercosProductCategoryService->create($mercosProductCategory);
        }

        $subcategory->update([
            'mercos_id' => $response->mercosId
        ]);
    }

    /**
     * Update a single subcategory.
     *
     * @param  \App\Models\Subcategory $subcategory
     * @param  mixed $erpFlexApiSubcategory
     * @return void
     */
    protected function updateSingleSubcategory(Subcategory $subcategory, mixed $erpFlexApiSubcategory): void
    {
        $mercosProductCategory = new MercosProductCategory(
            nome: $erpFlexApiSubcategory->SBA_Desc,
            id: $subcategory->mercos_id,
            categoria_pai_id: $subcategory->category->mercos_id ?? null
        );

        $response = $this->mercosProductCategoryService->update($subcategory->mercos_id, $mercosProductCategory);

        sleep($response->delayForRateLimiting);

        if (!$response->success) {
            $response = $this->mercosProductCategoryService->update($subcategory->mercos_id, $mercosProductCategory);
        }
    }
}
