<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexOrder;
use App\Http\Integrations\ErpFlex\ErpFlexConnector;
use App\Http\Integrations\ErpFlex\Requests\Order\ErpFlexOrderCreateOrderRequest;

class ErpFlexOrderService extends ErpFlexBaseService
{
    use HandlesLogCreation;

    /**
     * Create a new order.
     *
     * @param  \App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexOrder $erpFlexOrder
     * @return mixed
     */
    public function create(ErpFlexOrder $erpFlexOrder): mixed
    {
        $response = (new ErpFlexConnector())
            ->request(new ErpFlexOrderCreateOrderRequest($erpFlexOrder))
            ->send();

        $body = json_decode($response->body());

        if (is_null($body)) {
            $this->createLog(
                $response,
                false,
                'erp_flex_order_service.create.empty_body',
                $erpFlexOrder->toArray(true)
            );

            return null;
        }

        $this->createLog(
            $response,
            ($body->success === 1),
            null,
            $erpFlexOrder->toArray(true)
        );

        return !empty($body->data)
            ? $body->data
            : null;
    }
}
