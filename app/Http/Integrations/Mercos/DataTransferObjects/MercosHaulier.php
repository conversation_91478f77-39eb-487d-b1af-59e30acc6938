<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosHaulier
{
    /**
     * Create a new instance.
     *
     * @param  string|null $nome
     * @param  int|null $id
     * @param  string|null $cidade
     * @param  string|null $estado
     * @param  string|null $informacoes_adicionais
     * @param  array|null $telefones
     * @param  bool|null $excluido
     * @param  string|null $ultima_alteracao
     */
    public function __construct(
        public ?string $nome,
        public ?int $id = null,
        public ?string $cidade = null,
        public ?string $estado = null,
        public ?string $informacoes_adicionais = null,
        public ?array $telefones = null,
        public ?bool $excluido = null,
        public ?string $ultima_alteracao = null
    ) {
    }

    /**
     * Create a new instance from an object.
     *
     * @param  object $object
     * @return static
     */
    public static function fromObject(object $object): static
    {
        return new static(
            nome: $object->nome,
            id: $object->id,
            cidade: $object->cidade,
            estado: $object->estado,
            informacoes_adicionais: $object->informacoes_adicionais,
            telefones: $object->telefones,
            excluido: $object->excluido,
            ultima_alteracao: $object->ultima_alteracao
        );
    }
}
