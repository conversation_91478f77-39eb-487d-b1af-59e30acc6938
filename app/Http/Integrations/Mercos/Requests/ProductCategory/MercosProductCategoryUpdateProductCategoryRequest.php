<?php

namespace App\Http\Integrations\Mercos\Requests\ProductCategory;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosProductCategory;
use App\Http\Integrations\Mercos\Requests\MercosBasePutRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosProductCategoryUpdateProductCategoryRequest extends MercosBasePutRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosProductCategory $mercosProductCategory
     */
    public function __construct(
        protected int $id,
        protected MercosProductCategory $mercosProductCategory
    ) {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/categorias/' . $this->id;
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return array_filter(
            $this->mercosProductCategory->toArray(true)
        );
    }
}
