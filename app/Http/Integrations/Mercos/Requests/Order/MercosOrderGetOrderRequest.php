<?php

namespace App\Http\Integrations\Mercos\Requests\Order;

use App\Http\Integrations\Mercos\Requests\MercosBaseGetRequest;

class MercosOrderGetOrderRequest extends MercosBaseGetRequest
{
    /**
     * Create a new instance.
     *
     * @param  int $id
     */
    public function __construct(protected int $id)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v2/pedidos/' . $this->id;
    }
}
