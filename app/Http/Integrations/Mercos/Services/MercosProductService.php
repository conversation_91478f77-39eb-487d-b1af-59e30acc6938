<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosProduct;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosProductResponse;
use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\Product\MercosProductCreateProductRequest;
use App\Http\Integrations\Mercos\Requests\Product\MercosProductDeleteProductRequest;
use App\Http\Integrations\Mercos\Requests\Product\MercosProductGetProductRequest;
use App\Http\Integrations\Mercos\Requests\Product\MercosProductGetProductsRequest;
use App\Http\Integrations\Mercos\Requests\Product\MercosProductUpdateProductRequest;
use App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse;
use App\Http\Integrations\Mercos\Services\Interfaces\Getable;

class MercosProductService extends MercosBaseService implements Getable
{
    use HandlesLogCreation;

    /**
     * Get all products.
     *
     * @param  string|null $filters
     * @return \App\Http\Integrations\Mercos\Response\MercosGetCollectionDefaultResponse
     */
    public function get(?string $filters = null): MercosGetCollectionDefaultResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosProductGetProductsRequest($filters))
            ->send();

        $this->createLog($response);

        $body = json_decode($response->body());

        if ($response->status() === 429) {
            $delayForRateLimiting = (int) $body->tempo_ate_permitir_novamente + 1;
        }

        return new MercosGetCollectionDefaultResponse(
            is_array($body) ? $body : [],
            $delayForRateLimiting,
            (int) $response->header('MEUSPEDIDOS_REQUISICOES_EXTRAS'),
            $response->successful()
        );
    }

    /**
     * Get a product by its ID.
     *
     * @param  int $id
     * @return mixed
     */
    public function getById(int $id): mixed
    {
        return json_decode(
            (new MercosConnector($this->applicationToken, $this->companyToken))
                ->request(new MercosProductGetProductRequest($id))
                ->send()
                ->body()
        );
    }

    /**
     * Create a product.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosProduct $mercosProduct
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosProductResponse
     */
    public function create(MercosProduct $mercosProduct): MercosProductResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $connector = new MercosConnector($this->applicationToken, $this->companyToken);

        $request = new MercosProductCreateProductRequest($mercosProduct);

        $response = $connector
            ->request($request)
            ->send();

        $body = json_decode($response->body());

        $this->createLog(
            $response,
            ($response->status() === 200),
            null,
            $request->defaultData()
        );

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosProductResponse(
            mercosId: !empty($response->header('MeusPedidosID'))
                ? $response->header('MeusPedidosID')
                : null,
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 201
        );
    }

    /**
     * Update a product.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosProduct $mercosProduct
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosProductResponse
     */
    public function update(int $id, MercosProduct $mercosProduct): MercosProductResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $request = new MercosProductUpdateProductRequest($id, $mercosProduct);

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request($request)
            ->send();

        $this->createLog(
            $response,
            ($response->status() === 200),
            null,
            $request->defaultData()
        );

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosProductResponse(
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 200
        );
    }

    /**
     * Delete a product.
     *
     * @param  int $id
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosProduct $mercosProduct
     * @return bool
     */
    public function delete(int $id, MercosProduct $mercosProduct): bool
    {
        return (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosProductDeleteProductRequest($id, $mercosProduct))
            ->send()
            ->ok();
    }
}
