<?php

namespace App\Http\Integrations\Mercos\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\Mercos\DataTransferObjects\MercosCustomerXSalesman;
use App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosCustomerXSalesmanResponse;
use App\Http\Integrations\Mercos\MercosConnector;
use App\Http\Integrations\Mercos\Requests\CustomerXSalesman\MercosCustomerCreateCustomerXSalesmanRequest;

class MercosCustomerXSalesmanService extends MercosBaseService
{
    use HandlesLogCreation;

    /**
     * Create a customer.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosCustomerXSalesman $mercosCustomer
     * @return \App\Http\Integrations\Mercos\DataTransferObjects\Response\MercosCustomerXSalesmanResponse
     */
    public function create(MercosCustomerXSalesman $mercosCustomer): MercosCustomerXSalesmanResponse
    {
        $delayForRateLimiting = self::DELAY_FOR_RATE_LIMITING;

        $response = (new MercosConnector($this->applicationToken, $this->companyToken))
            ->request(new MercosCustomerCreateCustomerXSalesmanRequest($mercosCustomer))
            ->send();

        $this->createLog($response);

        if ($response->status() === 429) {
            $delayForRateLimiting = json_decode($response->body())->tempo_ate_permitir_novamente + 1;
        }

        return new MercosCustomerXSalesmanResponse(
            mercosId: !empty($response->header('MeusPedidosID'))
                ? $response->header('MeusPedidosID')
                : null,
            delayForRateLimiting: $delayForRateLimiting,
            success: $response->status() === 201
        );
    }
}
