<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\ErpFlex\ErpFlexConnector;
use App\Http\Integrations\ErpFlex\Requests\Product\ErpFlexProductGetProductsRequest;

class ErpFlexProductService extends ErpFlexBaseService
{
    /**
     * Get all products.
     *
     * @param  int|null $limit
     * @param  int|null $offset
     * @param  string|null $filters
     * @return mixed
     */
    public function get(?int $limit = null, ?int $offset = null, ?string $filters = null): mixed
    {
        return json_decode(
            (new ErpFlexConnector($this->username, $this->password))
                ->request(new ErpFlexProductGetProductsRequest($limit, $offset, $filters))
                ->send()
        );
    }
}
