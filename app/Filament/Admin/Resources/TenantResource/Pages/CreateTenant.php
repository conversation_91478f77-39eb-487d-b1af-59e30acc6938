<?php

namespace App\Filament\Admin\Resources\TenantResource\Pages;

use App\Filament\Admin\Resources\TenantResource;
use App\Models\Tenant;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateTenant extends CreateRecord
{
    protected static string $resource = TenantResource::class;

    /** @inheritDoc */
    protected function handleRecordCreation(array $data): Model
    {
        $tenant = Tenant::create(['id' => $data['id']]);

        $tenant->update([
            'mercos' => [
                'api' => [
                    'company_token' => base_encrypt($data['mercos_api_company_token']),
                    'application_token' => config('mercos.api.token.application'),
                ],
                'connection_mode' => $data['mercos_connection_mode'],
            ],
            'erp_flex' => [
                'api' => [
                    'username' => base_encrypt($data['erp_flex_api_username']),
                    'password' => base_encrypt($data['erp_flex_api_password']),
                ],
                'company_id' => $data['erp_flex_tenant_id'],
                'multi_database' => $data['erp_flex_multi_database'],
                'connection_mode' => $data['erp_flex_connection_mode'],
            ],
        ]);

        $tenant->domains()->create(['domain' => $data['id'] . '.integracoesp4dev.com.br']);

        return $tenant;
    }
}
