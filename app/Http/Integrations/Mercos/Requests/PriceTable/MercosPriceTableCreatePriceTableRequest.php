<?php

namespace App\Http\Integrations\Mercos\Requests\PriceTable;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTable;
use App\Http\Integrations\Mercos\Requests\MercosBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class MercosPriceTableCreatePriceTableRequest extends MercosBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Mercos\DataTransferObjects\MercosPriceTable $mercosPriceTable
     */
    public function __construct(protected MercosPriceTable $mercosPriceTable)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/tabelas_preco';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return $this->mercosPriceTable->toArray(true);
    }
}
