<?php

namespace App\Actions\Mercos;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosOrderStatus;
use App\Http\Integrations\Mercos\Services\MercosOrderService;
use App\Models\Billing;
use App\Models\ErpFlex\SD2;
use App\Models\ErpFlex\SF2;
use App\Models\IntegrationSetting;
use App\Models\Order;
use App\Models\OrderBilling;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreateOrderBillingsFromErpFlex
{
    use AsAction;

    protected bool $force = false;

    protected IntegrationSetting $integrationSetting;
    protected MercosOrderService $mercosOrderService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue('erp_flex_order_' . tenant('id'));
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @param  int|null $erpFlexId
     * @return void
     */
    public function handle(bool $force = false, ?int $erpFlexId = null): void
    {
        $this->integrationSetting = integration_settings();

        if (!$this->integrationSetting->enables_orders_automation || !$this->integrationSetting->enables_order_billings_automation) {
            return;
        }

        $this->force = $force;
        $this->mercosOrderService = new MercosOrderService();

        $this->integrateInvoices($erpFlexId);
    }

    /**
     * Integrate the invoices.
     *
     * @param  int|null $erpFlexId
     * @return void
     */
    protected function integrateInvoices(?int $erpFlexId = null): void
    {
        $executionStart = now();

        $lastErpFlexOrdersUpdate = null;

        if ($this->integrationSetting->last_erp_flex_orders_update) {
            $lastErpFlexOrdersUpdate = Carbon::parse($this->integrationSetting->last_erp_flex_orders_update)->setTimezone('America/Sao_Paulo');
        } else {
            $this->force = true;
        }

        $orderIds = Order::query()
            ->whereNotNull('erp_flex_id')
            ->get()
            ->pluck('erp_flex_id')
            ->toArray();

        SF2::query()
            ->whereIn('SF2_IDSC5', $orderIds)
            ->when($erpFlexId, fn(Builder $query): Builder => $query->where('SF2_ID', $erpFlexId))
            ->when(!$this->force, function (Builder $query) use ($lastErpFlexOrdersUpdate): Builder {
                return $query->where(function (Builder $query) use ($lastErpFlexOrdersUpdate): Builder {
                    return $query
                        ->where('SF2_DT_INC', '>=', $lastErpFlexOrdersUpdate)
                        ->orWhere('SF2_DT_ALT', '>=', $lastErpFlexOrdersUpdate);
                });
            })
            ->when($this->force, function (Builder $query): Builder {
                return $query->where(function (Builder $query): Builder {
                    return $query
                        ->where('SF2_DT_INC', '>=', carbon($this->integrationSetting->erp_flex_order_billings_cutoff_date)->format('Y-m-d H:i:s'))
                        ->orWhere('SF2_DT_ALT', '>=', carbon($this->integrationSetting->erp_flex_order_billings_cutoff_date)->format('Y-m-d H:i:s'));
                });
            })
            ->lazyById(1000, 'SF2_ID')
            ->each(function (SF2 $erpFlexApiInvoice): void {
                try {
                    $this->processSingleInvoice($erpFlexApiInvoice);
                } catch (Throwable $th) {
                    error($th);
                }
            });

        $this->integrationSetting->update(['last_erp_flex_orders_update' => $executionStart]);
    }

    /**
     * Process a single invoice.
     *
     * @param  \App\Models\ErpFlex\SF2 $sf2
     * @return void
     */
    public function processSingleInvoice(SF2 $sf2): void
    {
        $invoiceData = [
            'erp_flex_data' => [
                'SF2_ID' => $sf2->SF2_ID,
                'SF2_IDSF3' => $sf2->SF2_IDSF3,
                'SF2_IDSC5' => $sf2->SF2_IDSC5,
                'SF2_NrNFe' => $sf2->SF2_NrNFe,
                'sf3' => [
                    'SF3_ID' => $sf2->sf3?->SF3_ID ?? '',
                    'SF3_IDSF2' => $sf2->sf3?->SF3_IDSF2 ?? '',
                    'SF3_ValNF' => $sf2->sf3?->SF3_ValNF ?? '',
                    'SF3_DtEntSai' => $sf2->sf3?->SF3_DtEntSai ?? '',
                    'SF3_NrNFSe' => $sf2->sf3?->SF3_NrNFSe ?? '',
                ],
                'sd2s' => $sf2->sd2s
                    ->map(fn(SD2 $sd2): array => [
                        'SD2_ID' => $sd2->SD2_ID,
                        'SF2_IDSC5' => $sd2->SF2_IDSC5,
                        'SD2_ValItem' => $sd2->SD2_ValItem,
                    ])
                    ->toArray(),
            ],
        ];

        /** @var \App\Models\Billing $billing */
        $billing = Billing::updateOrCreate(
            ['erp_flex_id' => $sf2->SF2_ID],
            ['erp_flex_data' => $invoiceData]
        );

        $sf2SC5Ids = $sf2->sd2s()
            ->select(['SD2_IDSC5'])
            ->get()
            ->pluck('SD2_IDSC5')
            ->toArray();

        $orderIds = Order::query()
            ->select(['id'])
            ->whereIn('erp_flex_id', $sf2SC5Ids)
            ->get()
            ->pluck('id')
            ->toArray();

        foreach ($orderIds as $orderId) {
            /** @var \App\Models\OrderBilling $orderBilling */
            $orderBilling = OrderBilling::firstOrCreate([
                'order_id' => $orderId,
                'billing_id' => $billing->id,
            ], [
                'erp_flex_billing_id' => $billing->erp_flex_id,
            ]);

            if ($orderBilling->mercos_billing_id) {
                continue;
            }

            $this->createSingleInvoice($orderBilling, $sf2);
        }
    }

    /**
     * Create a single invoice.
     *
     * @param  \App\Models\OrderBilling $orderBilling
     * @param  \App\Models\ErpFlex\SF2 $sf2
     * @return void
     */
    protected function createSingleInvoice(OrderBilling $orderBilling, SF2 $sf2): void
    {
        $billedAmount = $sf2->sd2s()
            ->where('SD2_IDSC5', $orderBilling->order->erp_flex_id)
            ->get()
            ->sum(fn(SD2 $sd2): float => (float) $sd2->SD2_ValItemNFe);

        $mercosInvoiceObject = new MercosOrderStatus(
            pedido_id: $orderBilling->order->mercos_id,
            valor_faturado: $billedAmount,
            data_faturamento: carbon($sf2->SF2_Emissao)->format('Y-m-d'),
            numero_nf: $sf2->SF2_NrNFe,
        );

        $response = $this->mercosOrderService->createBilling($mercosInvoiceObject);

        if (is_null($response->mercosId) && !is_null($response->delayForRateLimiting) && $response->delayForRateLimiting > 0) {
            $response = $this->mercosOrderService->createBilling($mercosInvoiceObject);
        }

        if (!is_null($response->mercosId)) {
            $orderBilling->update(['mercos_billing_id' => $response->mercosId]);
        }
    }
}
