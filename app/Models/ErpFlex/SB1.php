<?php

namespace App\Models\ErpFlex;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class SB1 extends BaseModel
{
    protected $table = 'SB1';
    protected $primaryKey = 'SB1_ID';
    protected $prefix = 'SB1';

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'SB1_DT_INC' => 'datetime',
        'SB1_DT_ALT' => 'datetime',
    ];

    public function sba(): BelongsTo
    {
        return $this->belongsTo(SBA::class, 'SB1_IDSBA', 'SBA_ID');
    }

    public function ncm(): BelongsTo
    {
        return $this->belongsTo(NCM::class, 'SB1_IDNCM', 'NCM_ID');
    }

    public function sb1v(): HasOne
    {
        return $this->hasOne(SB1v::class, 'SB1_ID', 'SB1_ID');
    }

    public function sb2(): HasMany
    {
        return $this->hasMany(SB2::class, 'SB2_IDSB1', 'SB1_ID');
    }
}
