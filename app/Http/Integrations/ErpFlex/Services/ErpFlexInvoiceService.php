<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\Concerns\HandlesLogCreation;
use App\Http\Integrations\ErpFlex\ErpFlexConnector;
use App\Http\Integrations\ErpFlex\Requests\Invoice\ErpFlexCustomerGetInvoiceRequest;
use Exception;

class ErpFlexInvoiceService extends ErpFlexBaseService
{
    use HandlesLogCreation;

    /**
     * Get a customer by its ID.
     *
     * @param  int $id
     * @return mixed
     */
    public function getById(int $id): mixed
    {
        $response = (new ErpFlexConnector($this->username, $this->password))
            ->request(new ErpFlexCustomerGetInvoiceRequest($id))
            ->send();

        $body = json_decode($response->body());

        if (is_null($body)) {
            $this->createLog(
                $response,
                false,
                'erp_flex_invoice_service.get_by_id.empty_body'
            );

            throw new Exception('erp_flex_invoice_service.get_by_id.empty_body');
        }

        $this->createLog(
            $response,
            ($body->status === true)
        );

        return $body->data;
    }
}
