<?php

namespace App\Models\ErpFlex;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

 /**
 * SF3 (Invoice headers) Model.
 *
 * @package App\Models\ErpFlex;
 *
 * @property  int $SF3_IDSF2
 * @property  string $SF3_ValNF
 */
class SF3 extends BaseModel
{
    protected $table = 'SF3';
    protected $primaryKey = 'SF3_ID';
    protected $prefix = 'SF3';

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'SF3_DT_INC' => 'datetime',
        'SF3_DT_ALT' => 'datetime',
    ];

    public function sf2(): BelongsTo
    {
        return $this->belongsTo(SF2::class, 'SF3_IDSF2', 'SF2_ID');
    }
}
