<?php

namespace App\Http\Integrations\Mercos\DataTransferObjects;

class MercosPriceTable
{
    /**
     * Create a new instance.
     *
     * @param  string $nome
     * @param  string $tipo
     * @param  int|null $id
     * @param  float|null $acrescimo
     * @param  float|null $desconto
     * @param  bool|null $excluido
     * @param  string|null $ultima_alteracao
     */
    public function __construct(
        public string $nome,
        public string $tipo,
        public ?int $id = null,
        public ?float $acrescimo = null,
        public ?float $desconto = null,
        public ?bool $excluido = null,
        public ?string $ultima_alteracao = null
    ) {
    }

    /**
     * @inheritDoc
     */
    public function toArray(bool $write = false): array
    {
        $data = [
            'nome' => $this->nome,
            'tipo' => $this->tipo,
            'id' => $this->id,
            'acrescimo' => $this->acrescimo,
            'desconto' => $this->desconto,
            'excluido' => $this->excluido,
            'ultima_alteracao' => $this->ultima_alteracao
        ];

        if ($write) {
            unset($data['id']);
            unset($data['excluido']);
            unset($data['ultima_alteracao']);
        }

        return $data;
    }
}
