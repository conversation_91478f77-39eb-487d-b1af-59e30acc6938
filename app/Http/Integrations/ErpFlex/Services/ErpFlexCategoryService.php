<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\ErpFlex\ErpFlexConnector;
use App\Http\Integrations\ErpFlex\Requests\Category\ErpFlexCategoryGetCategoriesRequest;

class ErpFlexCategoryService extends ErpFlexBaseService
{
    /**
     * Get all categories.
     *
     * @param  int $page
     * @return mixed
     */
    public function get(int $page = 1): mixed
    {
        return json_decode(
            (new ErpFlexConnector($this->username, $this->password))
                ->request(new ErpFlexCategoryGetCategoriesRequest($page))
                ->send()
        );
    }
}
