<?php

namespace App\Filament\Resources\CategoryResource\Concerns;

use App\Models\Category;
use Filament\Tables\Columns\TextColumn;

trait HandlesCategoryResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')->label('ID'),
            TextColumn::make('erp_flex_id')->label('ID ERPFlex'),
            TextColumn::make('mercos_id')->label('ID Mercos'),
            TextColumn::make('name')
                ->label('Nome')
                ->formatStateUsing(fn (Category $record): string => $record->erp_flex_data['SBA_Desc']),
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [];
    }
}
