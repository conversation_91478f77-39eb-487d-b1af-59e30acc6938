<?php

namespace App\Actions\Mercos;

use App\Http\Integrations\Mercos\DataTransferObjects\MercosPaymentCondition;
use App\Http\Integrations\Mercos\Services\MercosPaymentConditionService;
use App\Models\ErpFlex\SEP;
use App\Models\PaymentCondition;
use App\Models\IntegrationSetting;
use App\Models\PaymentConditionPairing;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class CreatePaymentConditionsFromErpFlex
{
    use AsAction;

    protected bool $force = false;
    protected bool $pairing = false;

    protected MercosPaymentConditionService $mercosPaymentConditionService;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue('erp_flex_management_' . tenant('id'));
    }

    /**
     * Handle the action.
     *
     * @param  bool $force
     * @return void
     */
    public function handle(bool $force = false): void
    {
        if (!integration_settings()->enables_payment_conditions_automation) {
            return;
        }

        $this->pairing = false;

        if (!integration_settings()->payment_conditions_pairing_finished) {
            $this->pairing = true;
        }

        $this->force = $force;
        $this->mercosPaymentConditionService = new MercosPaymentConditionService();

        $this->integrateConditionPayments();
    }

    /**
     * Integrate all payment conditions from ERPFlex to Mercos.
     *
     * @return void
     */
    protected function integrateConditionPayments(): void
    {
        try {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();
            $executionStart = now();

            $updateStartedAt = Carbon::parse($integrationSetting->last_payment_condition_update)
                ->setTimezone('America/Sao_Paulo');

            SEP::query()
                ->when(!$this->force, function (Builder $query) use ($updateStartedAt) : Builder {
                    return $query->where(function (Builder $query) use ($updateStartedAt) : Builder {
                        return $query
                            ->where('SEP_DT_INC', '>=', $updateStartedAt)
                            ->orWhere('SEP_DT_ALT', '>=', $updateStartedAt);
                    });
                })
                ->lazyById(1000)
                ->each(function (mixed $erpFlexApiPaymentCondition): void {
                    $this->processSinglePaymentCondition($erpFlexApiPaymentCondition);
                });

            $integrationSetting->update(['last_payment_condition_update' => $executionStart]);
        } catch (Throwable $th) {
            error($th);
        }
    }

    /**
     * Process a single payment condition.
     *
     * @param  \App\Models\ErpFlex\SEP $erpFlexApiPaymentCondition
     * @return void
     */
    protected function processSinglePaymentCondition(SEP $erpFlexApiPaymentCondition): void
    {
        if ($this->pairing) {
            PaymentConditionPairing::updateOrCreate(
                ['erp_flex_id' => $erpFlexApiPaymentCondition->SEP_ID],
                [
                    'erp_flex_data' => json_decode(json_encode($erpFlexApiPaymentCondition), true),
                    'source' => 'erpflex',
                ],
            );

            return;
        }

        /** @var \App\Models\PaymentCondition $paymentCondition */
        $paymentCondition = PaymentCondition::updateOrCreate(
            ['erp_flex_id' => $erpFlexApiPaymentCondition->SEP_ID],
            [
                'erp_flex_data' => json_decode(json_encode($erpFlexApiPaymentCondition), true),
                'source' => 'erpflex',
            ],
        );

        if (!$paymentCondition->mercos_id) {
            $this->createSinglePaymentCondition($paymentCondition, $erpFlexApiPaymentCondition);
            return;
        }

        $this->updateSinglePaymentCondition($paymentCondition, $erpFlexApiPaymentCondition);
    }

    /**
     * Create a single payment condition.
     *
     * @param  \App\Models\PaymentCondition $paymentCondition
     * @param  \App\Models\ErpFlex\SEP $erpFlexApiPaymentCondition
     * @return void
     */
    protected function createSinglePaymentCondition(PaymentCondition $paymentCondition, SEP $erpFlexApiPaymentCondition): void
    {
        $mercosPaymentObject = new MercosPaymentCondition(
            nome: trim($erpFlexApiPaymentCondition->SEP_Desc),
            valor_minimo: null, // @todo temos valor minimo parcela. Confirmar.
            excluido: false
        );

        $response = $this->mercosPaymentConditionService->create($mercosPaymentObject);

        sleep($response->delayForRateLimiting);

        if (is_null($response->mercosId)) {
            $response = $this->mercosPaymentConditionService->create($mercosPaymentObject);
        }

        $paymentCondition->update([
            'mercos_id' => $response->mercosId
        ]);
    }

    /**
     * Update a single payment condition.
     *
     * @param  \App\Models\PaymentCondition $paymentCondition
     * @param  mixed $erpFlexApiPaymentCondition
     * @return void
     */
    protected function updateSinglePaymentCondition(PaymentCondition $paymentCondition, SEP $erpFlexApiPaymentCondition): void
    {
        $mercosPaymentObject = new MercosPaymentCondition(
            id: $paymentCondition->mercos_id,
            nome: trim($erpFlexApiPaymentCondition->SEP_Desc),
        );

        $response = $this->mercosPaymentConditionService->update($paymentCondition->mercos_id, $mercosPaymentObject);

        sleep($response->delayForRateLimiting);

        if (!$response->success) {
            $response = $this->mercosPaymentConditionService->update($paymentCondition->mercos_id, $mercosPaymentObject);
        }
    }
}
