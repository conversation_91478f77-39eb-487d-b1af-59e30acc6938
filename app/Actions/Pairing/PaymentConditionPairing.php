<?php

namespace App\Actions\Pairing;

use App\Http\Integrations\Mercos\Services\MercosPaymentConditionService;
use App\Models\IntegrationSetting;
use App\Models\PaymentCondition;
use App\Models\PaymentConditionPairing as PaymentConditionPairingModel;
use Exception;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class PaymentConditionPairing
{
    use AsAction;

    protected MercosPaymentConditionService $mercosPaymentConditionService;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(tenant('id') . '_cadastros');
    }

    /**
     * Handle the action.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            if (integration_settings()->payment_conditions_pairing_finished) {
                throw new Exception('payment_conditions_pairing.pairing_should_only_be_done_once');
                return;
            }

            $pairingCollection = PaymentConditionPairingModel::get();

            $pairingCollection->each(function (PaymentConditionPairingModel $pairing): void {
                if (!$pairing->mercos_id) {
                    return;
                }

                PaymentCondition::create([
                    'erp_flex_id' => $pairing->erp_flex_id,
                    'erp_flex_data' => $pairing->erp_flex_data,
                    'mercos_id' => $pairing->mercos_id,
                    'mercos_data' => $pairing->mercos_data,
                ]);
            });

            $integrationSetting = IntegrationSetting::first();
            $integrationSetting->payment_conditions_pairing_finished = true;
            $integrationSetting->save();
        } catch (Throwable $th) {
            error($th);
        }
    }
}
